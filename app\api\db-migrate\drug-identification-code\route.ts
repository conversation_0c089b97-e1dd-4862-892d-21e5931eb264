import { NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

/**
 * 数据库迁移：将药品信息表的条形码字段改为药品标识码字段
 * POST /api/db-migrate/drug-identification-code
 */
export async function POST() {
  try {
    console.log('开始数据库迁移：条形码字段 → 药品标识码字段');

    // 1. 检查药品信息表是否存在
    const tableExists = await query(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='药品信息'"
    );

    if (!tableExists || tableExists.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '药品信息表不存在，无法执行迁移'
        },
        { status: 404 }
      );
    }

    // 2. 检查当前表结构
    const currentColumns = await query("PRAGMA table_info(药品信息)");
    console.log('当前表结构:', currentColumns);

    // 检查是否已经有药品标识码字段
    const hasDrugIdCode = currentColumns.some((col: any) => col.name === '药品标识码');
    const hasBarcode = currentColumns.some((col: any) => col.name === '条形码');

    if (hasDrugIdCode && !hasBarcode) {
      return NextResponse.json({
        success: true,
        message: '数据库已经完成迁移，药品标识码字段已存在',
        data: { already_migrated: true }
      });
    }

    // 3. 开始事务
    await run('BEGIN TRANSACTION');

    try {
      // 4. 备份现有数据
      const existingData = await query('SELECT * FROM 药品信息');
      console.log(`备份了 ${existingData.length} 条药品数据`);

      // 5. 重命名原表
      await run('ALTER TABLE 药品信息 RENAME TO 药品信息_备份');
      console.log('原表已重命名为 药品信息_备份');

      // 6. 创建新的表结构（使用药品标识码替代条形码）
      await run(`
        CREATE TABLE 药品信息 (
          编号 INTEGER PRIMARY KEY AUTOINCREMENT,
          名称 TEXT NOT NULL,
          通用名 TEXT,
          描述 TEXT,
          药品标识码 TEXT,
          分类编号 INTEGER,
          供应商编号 INTEGER,
          生产厂家 TEXT,
          批准文号 TEXT,
          规格 TEXT,
          剂型 TEXT,
          售价 DECIMAL(10,2) DEFAULT 0,
          成本价 DECIMAL(10,2) DEFAULT 0,
          库存数量 INTEGER DEFAULT 0,
          最低库存 INTEGER DEFAULT 0,
          是否处方药 BOOLEAN DEFAULT 0,
          是否医保 BOOLEAN DEFAULT 0,
          储存条件 TEXT DEFAULT '常温',
          状态 TEXT DEFAULT 'active',
          创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (分类编号) REFERENCES 药品分类(编号),
          FOREIGN KEY (供应商编号) REFERENCES 供应商信息表(编号)
        )
      `);
      console.log('新表结构创建成功');

      // 7. 迁移数据（将条形码数据转换为药品标识码）
      if (existingData.length > 0) {
        for (const record of existingData) {
          // 如果原来的条形码是药品追溯码格式，提取前7位作为药品标识码
          let drugIdCode = record.条形码 || '';
          if (drugIdCode && drugIdCode.length >= 7) {
            // 检查是否为数字格式的追溯码
            if (/^\d+$/.test(drugIdCode) && drugIdCode.length > 7) {
              drugIdCode = drugIdCode.substring(0, 7);
            }
          }

          await run(`
            INSERT INTO 药品信息 (
              编号, 名称, 通用名, 描述, 药品标识码, 分类编号, 供应商编号,
              生产厂家, 批准文号, 规格, 剂型, 售价, 成本价, 库存数量, 最低库存,
              是否处方药, 是否医保, 储存条件, 状态, 创建时间, 更新时间
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            record.编号,
            record.名称,
            record.通用名,
            record.描述,
            drugIdCode, // 使用转换后的药品标识码
            record.分类编号,
            record.供应商编号,
            record.生产厂家,
            record.批准文号,
            record.规格,
            record.剂型,
            record.售价,
            record.成本价,
            record.库存数量,
            record.最低库存,
            record.是否处方药,
            record.是否医保,
            record.储存条件,
            record.状态,
            record.创建时间,
            record.更新时间
          ]);
        }
        console.log(`成功迁移 ${existingData.length} 条记录`);
      }

      // 8. 创建索引
      await run('CREATE INDEX IF NOT EXISTS idx_药品信息_药品标识码 ON 药品信息(药品标识码)');
      await run('CREATE INDEX IF NOT EXISTS idx_药品信息_名称 ON 药品信息(名称)');
      await run('CREATE INDEX IF NOT EXISTS idx_药品信息_分类编号 ON 药品信息(分类编号)');
      console.log('索引创建成功');

      // 9. 提交事务
      await run('COMMIT');
      console.log('数据库迁移完成');

      // 10. 验证迁移结果
      const newTableInfo = await query("PRAGMA table_info(药品信息)");
      const recordCount = await query("SELECT COUNT(*) as count FROM 药品信息");

      return NextResponse.json({
        success: true,
        message: '数据库迁移成功完成',
        data: {
          migrated_records: existingData.length,
          new_table_structure: newTableInfo,
          total_records: recordCount[0]?.count || 0,
          backup_table: '药品信息_备份'
        }
      });

    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      console.error('迁移过程中出错，事务已回滚:', error);
      throw error;
    }

  } catch (error) {
    console.error('数据库迁移失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '数据库迁移失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

/**
 * 回滚迁移：恢复原来的条形码字段
 * DELETE /api/db-migrate/drug-identification-code
 */
export async function DELETE() {
  try {
    console.log('开始回滚数据库迁移');

    // 检查备份表是否存在
    const backupExists = await query(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='药品信息_备份'"
    );

    if (!backupExists || backupExists.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '备份表不存在，无法回滚'
        },
        { status: 404 }
      );
    }

    await run('BEGIN TRANSACTION');

    try {
      // 删除当前表
      await run('DROP TABLE IF EXISTS 药品信息');
      
      // 恢复备份表
      await run('ALTER TABLE 药品信息_备份 RENAME TO 药品信息');
      
      await run('COMMIT');
      console.log('数据库迁移回滚成功');

      return NextResponse.json({
        success: true,
        message: '数据库迁移回滚成功，已恢复条形码字段'
      });

    } catch (error) {
      await run('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('数据库迁移回滚失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '数据库迁移回滚失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
