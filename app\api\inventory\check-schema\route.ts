import { NextResponse } from 'next/server';
import { query } from '@/lib/db';

/**
 * 检查库存管理相关的数据库表结构
 */
export async function GET() {
  try {
    // 查询所有表
    const tables = await query(
      "SELECT name FROM sqlite_master WHERE type='table' ORDER BY name"
    );
    
    const result: any = {
      all_tables: tables.map(t => t.name),
      batch_related_tables: {},
      issues: []
    };
    
    // 检查批次相关的表
    const batchRelatedTables = ['药品批次表', 'product_batches', '库存记录', '药品追溯码记录'];
    
    for (const tableName of batchRelatedTables) {
      try {
        // 检查表是否存在
        const tableExists = await query(
          "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
          [tableName]
        );
        
        if (tableExists && tableExists.length > 0) {
          // 获取表结构
          const tableInfo = await query(`PRAGMA table_info(${tableName})`);
          result.batch_related_tables[tableName] = {
            exists: true,
            columns: tableInfo,
            record_count: 0
          };
          
          // 获取记录数量
          try {
            const countResult = await query(`SELECT COUNT(*) as count FROM ${tableName}`);
            result.batch_related_tables[tableName].record_count = countResult[0]?.count || 0;
          } catch (countError) {
            result.batch_related_tables[tableName].record_count = 'Error counting records';
          }
        } else {
          result.batch_related_tables[tableName] = {
            exists: false,
            columns: [],
            record_count: 0
          };
          result.issues.push(`表 ${tableName} 不存在`);
        }
      } catch (error) {
        result.batch_related_tables[tableName] = {
          exists: false,
          error: error instanceof Error ? error.message : '未知错误'
        };
        result.issues.push(`检查表 ${tableName} 时出错: ${error instanceof Error ? error.message : '未知错误'}`);
      }
    }
    
    // 检查批次查询API使用的表结构问题
    if (!result.batch_related_tables['batch']?.exists) {
      result.issues.push('批次查询API使用的 batch 表不存在，这是导致批次功能失败的主要原因');
    }
    
    if (result.batch_related_tables['product_batches']?.exists) {
      result.issues.push('存在 product_batches 表，但批次查询API使用的是 batch 表，存在表名不一致问题');
    }
    
    // 检查库存记录表的批次号字段
    if (result.batch_related_tables['库存记录']?.exists) {
      const 库存记录Columns = result.batch_related_tables['库存记录'].columns;
      const hasBatchColumn = 库存记录Columns.some((col: any) => col.name === '批次号');
      if (hasBatchColumn) {
        result.issues.push('库存记录表有批次号字段，可以考虑直接使用此表进行批次管理');
      }
    }
    
    return NextResponse.json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('检查数据库表结构失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '检查数据库表结构失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
