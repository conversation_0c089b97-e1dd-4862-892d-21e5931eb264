import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

// 导入码上放心SDK
const ApiClient = require('../../../../mashangfangxin/index.js').ApiClient;

interface Setting {
  setting_name: string;
  setting_value: string;
}

/**
 * 从系统设置中获取码上放心开放平台配置
 */
async function getConfig() {
  try {
    const settings = await query('SELECT 设置名称 as setting_name, 设置值 as setting_value FROM 系统设置 WHERE 设置名称 IN (?, ?, ?, ?)', [
      '码上放心开放平台AppKey',
      '码上放心开放平台AppSecret',
      '码上放心开放平台Url',
      '码上放心开放平台RefEntId'
    ]) as Setting[];

    const config = {
      appkey: '',
      appsecret: '',
      url: 'http://gw.api.taobao.com/router/rest',
      ref_ent_id: ''
    };

    settings.forEach(setting => {
      switch (setting.setting_name) {
        case '码上放心开放平台AppKey':
          config.appkey = setting.setting_value;
          break;
        case '码上放心开放平台AppSecret':
          config.appsecret = setting.setting_value;
          break;
        case '码上放心开放平台Url':
          config.url = setting.setting_value;
          break;
        case '码上放心开放平台RefEntId':
          config.ref_ent_id = setting.setting_value;
          break;
      }
    });

    // 使用环境变量作为备用
    if (!config.appkey) {
      config.appkey = process.env.MASHANGFANGXIN_APPKEY || '';
    }
    if (!config.appsecret) {
      config.appsecret = process.env.MASHANGFANGXIN_APPSECRET || '';
    }
    if (!config.url) {
      config.url = process.env.MASHANGFANGXIN_URL || 'http://gw.api.taobao.com/router/rest';
    }
    if (!config.ref_ent_id) {
      config.ref_ent_id = process.env.MASHANGFANGXIN_REF_ENT_ID || '';
    }

    return config;
  } catch (error) {
    console.error('获取码上放心配置失败:', error);
    return {
      appkey: process.env.MASHANGFANGXIN_APPKEY || '',
      appsecret: process.env.MASHANGFANGXIN_APPSECRET || '',
      url: process.env.MASHANGFANGXIN_URL || 'http://gw.api.taobao.com/router/rest',
      ref_ent_id: process.env.MASHANGFANGXIN_REF_ENT_ID || ''
    };
  }
}

/**
 * 创建API客户端实例
 */
function createClient(config: any) {
  return new ApiClient({
    'appkey': config.appkey,
    'appsecret': config.appsecret,
    'url': config.url
  });
}

/**
 * 生成单据编号
 */
function generateBillNumber(type: 'IN' | 'OUT'): string {
  const now = new Date();
  const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
  const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
  const randomStr = Math.random().toString(36).substring(2, 8).toUpperCase();
  return `${type}-${dateStr}${timeStr}-${randomStr}`;
}

/**
 * 上传出入库单据到码上放心平台
 */
async function uploadBillToMashangfangxin(config: any, billData: any, recordInfo: any) {
  const client = createClient(config);

  return new Promise((resolve, reject) => {
    // 根据单据类型选择API接口
    const apiMethod = 'alibaba.alihealth.drugtrace.top.lsyd.uploadinoutbill';

    // 根据药品是否为处方药判断药品类型（简化逻辑：处方药视为特药，非处方药视为普药）
    const physicType = recordInfo.is_prescription ? '2' : '3'; // 2特药，3普药

    // 根据单据类型设置发货和收货企业
    let fromUserId, toUserId;
    if (billData.bill_type === '102') {
      // 采购入库：由于我们没有供应商的真实企业ID，使用其他入库类型
      // 改为使用 '113' 其他入库，这样可以避免发货企业与收货企业相同的限制
      billData.bill_type = '113'; // 113-其他入库
      fromUserId = config.ref_ent_id; // 发货企业
      toUserId = config.ref_ent_id; // 收货企业
    } else if (billData.bill_type === '201') {
      // 销售出库：发货企业是药店，收货企业是客户
      fromUserId = config.ref_ent_id; // 发货企业是药店
      toUserId = config.ref_ent_id; // 收货企业（暂时使用药店ID，实际应该是客户ID）
    } else {
      // 其他情况默认使用药店ID
      fromUserId = config.ref_ent_id;
      toUserId = config.ref_ent_id;
    }

    // 构建API调用参数
    const apiParams = {
      bill_code: billData.bill_code,
      bill_type: billData.bill_type,
      bill_time: billData.bill_time,
      physic_type: physicType,
      ref_user_id: config.ref_ent_id, // 上传单据企业的单位编码（药店）
      from_user_id: fromUserId, // 发货企业entId
      to_user_id: toUserId, // 收货企业entId
      trace_codes: billData.trace_codes.join(','), // 追溯码，多个码用逗号拼接
      client_type: '2' // 客户端类型，必须填2
    };

    console.log('上传出入库单据到码上放心平台，参数:', apiParams);

    client.execute(apiMethod, apiParams, function(error: any, response: any) {
      if (error) {
        console.error('上传出入库单据失败:', error);
        reject(error);
      } else {
        console.log('上传出入库单据成功，响应数据:', JSON.stringify(response, null, 2));

        // 检查响应中的msg_info和response_success
        if (response && response.response_success === false) {
          // 如果response_success为false，说明上传失败
          const errorMessage = response.msg_info || response.model || '上传失败';
          console.error('上传到码上放心平台失败:', errorMessage);
          reject(new Error(errorMessage));
        } else {
          resolve(response);
        }
      }
    });
  });
}

/**
 * POST 请求处理函数 - 上传出入库单据
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { type, inventory_id, trace_codes } = body;

    if (!type || !inventory_id) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数：type 和 inventory_id'
      }, { status: 400 });
    }

    if (!trace_codes || !Array.isArray(trace_codes) || trace_codes.length === 0) {
      return NextResponse.json({
        success: false,
        message: '缺少药品追溯码信息'
      }, { status: 400 });
    }

    // 获取码上放心平台配置
    const config = await getConfig();
    
    if (!config.appkey || !config.appsecret || !config.ref_ent_id) {
      return NextResponse.json({
        success: false,
        message: '码上放心平台配置不完整，请在系统设置中配置相关参数'
      }, { status: 400 });
    }

    // 获取库存记录详情
    const inventoryRecord = await query(
      `SELECT
        i.*,
        p.名称 as product_name,
        p.通用名 as generic_name,
        p.规格 as specification,
        p.生产厂家 as manufacturer,
        p.批准文号 as approval_number,
        p.是否处方药 as is_prescription,
        s.名称 as supplier_name
      FROM 库存记录 i
      LEFT JOIN 药品信息 p ON i.药品编号 = p.编号
      LEFT JOIN 供应商 s ON i.供应商编号 = s.编号
      WHERE i.编号 = ?`,
      [inventory_id]
    );

    if (!inventoryRecord || inventoryRecord.length === 0) {
      return NextResponse.json({
        success: false,
        message: '库存记录不存在'
      }, { status: 404 });
    }

    const record = inventoryRecord[0];
    
    // 生成单据编号
    const billCode = generateBillNumber(type === 'in' ? 'IN' : 'OUT');
    
    // 构建单据数据
    const billData = {
      bill_code: billCode,
      bill_type: type === 'in' ? '102' : '201', // 102-采购入库，201-销售出库
      bill_time: new Date().toISOString().slice(0, 19).replace('T', ' '), // 格式化为 yyyy-MM-dd HH:mm:ss
      trace_codes: trace_codes, // 追溯码数组
      supplier_ent_id: record.供应商编号 ? `supplier_${record.供应商编号}` : null, // 供应商企业ID（模拟）
      customer_ent_id: null // 客户企业ID（暂时为空）
    };

    try {
      // 上传到码上放心平台
      const uploadResult = await uploadBillToMashangfangxin(config, billData, record);
      
      // 记录上传状态到数据库
      await run(
        `UPDATE 库存记录 SET 
          码上放心单据号 = ?,
          码上放心上传状态 = ?,
          码上放心上传时间 = CURRENT_TIMESTAMP,
          码上放心响应 = ?
        WHERE 编号 = ?`,
        [billCode, 'success', JSON.stringify(uploadResult), inventory_id]
      );

      return NextResponse.json({
        success: true,
        message: '出入库单据上传成功',
        data: {
          bill_code: billCode,
          upload_result: uploadResult,
          trace_codes_count: trace_codes.length
        }
      });

    } catch (uploadError) {
      console.error('上传到码上放心平台失败:', uploadError);

      // 确保错误对象有message属性
      const errorMessage = uploadError instanceof Error ? uploadError.message : String(uploadError);

      // 记录失败状态到数据库
      await run(
        `UPDATE 库存记录 SET
          码上放心单据号 = ?,
          码上放心上传状态 = ?,
          码上放心上传时间 = CURRENT_TIMESTAMP,
          码上放心响应 = ?
        WHERE 编号 = ?`,
        [billCode, 'failed', JSON.stringify({ error: errorMessage }), inventory_id]
      );

      return NextResponse.json({
        success: false,
        message: '上传到码上放心平台失败: ' + errorMessage,
        data: {
          bill_code: billCode,
          error: errorMessage
        }
      }, { status: 500 });
    }

  } catch (error) {
    console.error('处理出入库单据上传请求失败:', error);
    return NextResponse.json({
      success: false,
      message: '处理请求失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}
