/**
 * 供应商相关的TypeScript类型定义
 */

// 供应商基础信息接口
export interface Supplier {
  id: number;
  name: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  address?: string;
  tax_number?: string;
  bank_account?: string;
  bank_name?: string;
  license_number?: string;
  license_expiry_date?: string;
  gsp_certificate?: string;
  gsp_expiry_date?: string;
  business_scope?: string;
  quality_officer?: string;
  cooperation_status: 'active' | 'suspended' | 'terminated';
  status: 'active' | 'inactive';
  notes?: string;
  created_at: string;
  updated_at: string;
  product_count?: number; // 关联的药品数量
}

// 创建供应商时的数据接口
export interface CreateSupplierData {
  name: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  address?: string;
  tax_number?: string;
  bank_account?: string;
  bank_name?: string;
  license_number?: string;
  license_expiry_date?: string;
  gsp_certificate?: string;
  gsp_expiry_date?: string;
  business_scope?: string;
  quality_officer?: string;
  cooperation_status?: 'active' | 'suspended' | 'terminated';
  status?: 'active' | 'inactive';
  notes?: string;
}

// 更新供应商时的数据接口
export interface UpdateSupplierData extends CreateSupplierData {
  id: number;
}

// 供应商表单数据接口
export interface SupplierFormData {
  name: string;
  contact_person: string;
  phone: string;
  email: string;
  address: string;
  tax_number: string;
  bank_account: string;
  bank_name: string;
  license_number: string;
  license_expiry_date: string;
  gsp_certificate: string;
  gsp_expiry_date: string;
  business_scope: string;
  quality_officer: string;
  cooperation_status: 'active' | 'suspended' | 'terminated';
  status: 'active' | 'inactive';
  notes: string;
}

// 供应商搜索/筛选参数接口
export interface SupplierSearchParams {
  keyword?: string;
  cooperation_status?: 'active' | 'suspended' | 'terminated' | 'all';
  status?: 'active' | 'inactive' | 'all';
  page?: number;
  pageSize?: number;
  sortBy?: 'name' | 'created_at' | 'updated_at';
  sortOrder?: 'asc' | 'desc';
}

// API响应接口
export interface SupplierApiResponse {
  success: boolean;
  message?: string;
  data?: Supplier | Supplier[];
  error?: string;
}

// 供应商列表响应接口
export interface SupplierListResponse {
  success: boolean;
  data: Supplier[];
  total?: number;
  page?: number;
  pageSize?: number;
  message?: string;
  error?: string;
}

// 供应商统计信息接口
export interface SupplierStats {
  total: number;
  active: number;
  suspended: number;
  terminated: number;
  with_products: number;
  without_products: number;
}

// 合作状态选项
export const COOPERATION_STATUS_OPTIONS = [
  { value: 'active', label: '正常合作', color: 'green' },
  { value: 'suspended', label: '暂停合作', color: 'yellow' },
  { value: 'terminated', label: '终止合作', color: 'red' }
] as const;

// 状态选项
export const STATUS_OPTIONS = [
  { value: 'active', label: '启用', color: 'green' },
  { value: 'inactive', label: '禁用', color: 'gray' }
] as const;

// 表单验证规则
export const SUPPLIER_VALIDATION_RULES = {
  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    message: '供应商名称为必填项，长度为2-100个字符'
  },
  contact_person: {
    required: true,
    minLength: 2,
    maxLength: 50,
    message: '联系人为必填项，长度为2-50个字符'
  },
  phone: {
    required: true,
    pattern: /^1[3-9]\d{9}$|^0\d{2,3}-?\d{7,8}$/,
    message: '联系电话为必填项，请输入正确的手机号码或固定电话'
  },
  email: {
    required: false,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message: '请输入正确的邮箱地址'
  },
  address: {
    required: false,
    maxLength: 200,
    message: '地址长度不能超过200个字符'
  },
  tax_number: {
    required: false,
    pattern: /^[A-Z0-9]{15,20}$/,
    message: '税号格式不正确，应为15-20位字母和数字组合'
  },
  bank_account: {
    required: false,
    pattern: /^\d{10,25}$/,
    message: '银行账号应为10-25位数字'
  },
  bank_name: {
    required: false,
    maxLength: 100,
    message: '开户银行名称不能超过100个字符'
  },
  license_number: {
    required: false,
    pattern: /^[A-Z0-9]{15,18}$/,
    message: '营业执照号格式不正确，应为15-18位字母和数字组合'
  },
  license_expiry_date: {
    required: false,
    message: '请选择正确的营业执照有效期'
  },
  gsp_certificate: {
    required: false,
    maxLength: 50,
    message: 'GSP证书号不能超过50个字符'
  },
  gsp_expiry_date: {
    required: false,
    message: '请选择正确的GSP证书有效期'
  },
  business_scope: {
    required: false,
    maxLength: 500,
    message: '经营范围不能超过500个字符'
  },
  quality_officer: {
    required: false,
    maxLength: 50,
    message: '质量负责人姓名不能超过50个字符'
  },
  notes: {
    required: false,
    maxLength: 1000,
    message: '备注信息不能超过1000个字符'
  }
} as const;
