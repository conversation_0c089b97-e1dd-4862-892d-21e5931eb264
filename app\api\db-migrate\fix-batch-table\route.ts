import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

/**
 * 修复药品批次表结构 - 添加缺失的字段
 */

export async function POST(request: NextRequest) {
  try {
    console.log('开始修复药品批次表结构...');

    // 1. 检查药品批次表是否存在
    const tableExists = await query(`SELECT name FROM sqlite_master WHERE type='table' AND name='药品批次表'`);
    
    if (!tableExists || tableExists.length === 0) {
      // 创建新的药品批次表
      console.log('创建药品批次表...');
      await run(`
        CREATE TABLE 药品批次表 (
          编号 INTEGER PRIMARY KEY AUTOINCREMENT,
          药品编号 INTEGER NOT NULL,
          批次号 TEXT NOT NULL,
          生产日期 DATE,
          有效期 DATE NOT NULL,
          入库数量 INTEGER NOT NULL DEFAULT 0,
          当前数量 INTEGER NOT NULL DEFAULT 0,
          供应商编号 INTEGER,
          入库成本价 DECIMAL(10,2),
          状态 TEXT DEFAULT 'active' CHECK (状态 IN ('active', 'expired', 'depleted', 'locked')),
          创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
          更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
          备注 TEXT
        )
      `);
      
      // 创建唯一约束
      await run(`CREATE UNIQUE INDEX idx_药品批次表_唯一 ON 药品批次表(药品编号, 批次号)`);
    } else {
      // 检查现有表结构
      const tableInfo = await query(`PRAGMA table_info(药品批次表)`);
      console.log('当前药品批次表结构:', tableInfo);
      
      const hasCurrentQuantity = tableInfo.some((col: any) => col.name === '当前数量');
      const hasInboundQuantity = tableInfo.some((col: any) => col.name === '入库数量');
      const hasInboundCostPrice = tableInfo.some((col: any) => col.name === '入库成本价');
      const hasStatus = tableInfo.some((col: any) => col.name === '状态');
      
      // 添加缺失的字段
      if (!hasCurrentQuantity) {
        console.log('添加当前数量字段...');
        await run(`ALTER TABLE 药品批次表 ADD COLUMN 当前数量 INTEGER NOT NULL DEFAULT 0`);
        
        // 如果有剩余数量字段，将数据迁移过来
        const hasRemainingQuantity = tableInfo.some((col: any) => col.name === '剩余数量');
        if (hasRemainingQuantity) {
          await run(`UPDATE 药品批次表 SET 当前数量 = COALESCE(剩余数量, 0)`);
        } else {
          // 如果有数量字段，将数据迁移过来
          const hasQuantity = tableInfo.some((col: any) => col.name === '数量');
          if (hasQuantity) {
            await run(`UPDATE 药品批次表 SET 当前数量 = COALESCE(数量, 0)`);
          }
        }
      }
      
      if (!hasInboundQuantity) {
        console.log('添加入库数量字段...');
        await run(`ALTER TABLE 药品批次表 ADD COLUMN 入库数量 INTEGER NOT NULL DEFAULT 0`);
        
        // 将现有的数量数据迁移到入库数量
        const hasQuantity = tableInfo.some((col: any) => col.name === '数量');
        if (hasQuantity) {
          await run(`UPDATE 药品批次表 SET 入库数量 = COALESCE(数量, 0)`);
        } else {
          await run(`UPDATE 药品批次表 SET 入库数量 = 当前数量`);
        }
      }
      
      if (!hasInboundCostPrice) {
        console.log('添加入库成本价字段...');
        await run(`ALTER TABLE 药品批次表 ADD COLUMN 入库成本价 DECIMAL(10,2)`);
        
        // 如果有成本价字段，将数据迁移过来
        const hasCostPrice = tableInfo.some((col: any) => col.name === '成本价');
        if (hasCostPrice) {
          await run(`UPDATE 药品批次表 SET 入库成本价 = 成本价`);
        }
      }
      
      if (!hasStatus) {
        console.log('添加状态字段...');
        await run(`ALTER TABLE 药品批次表 ADD COLUMN 状态 TEXT DEFAULT 'active'`);
      }
    }

    // 2. 确保药品库存表存在
    const stockTableExists = await query(`SELECT name FROM sqlite_master WHERE type='table' AND name='药品库存'`);
    
    if (!stockTableExists || stockTableExists.length === 0) {
      console.log('创建药品库存表...');
      await run(`
        CREATE TABLE 药品库存 (
          编号 INTEGER PRIMARY KEY AUTOINCREMENT,
          药品编号 INTEGER NOT NULL UNIQUE,
          当前库存 INTEGER NOT NULL DEFAULT 0,
          最低库存 INTEGER DEFAULT 0,
          最高库存 INTEGER DEFAULT 0,
          平均成本价 DECIMAL(10,2) DEFAULT 0,
          最后入库时间 DATETIME,
          最后出库时间 DATETIME,
          创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
          更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `);
      
      // 初始化药品库存数据
      await run(`
        INSERT OR IGNORE INTO 药品库存 (药品编号, 当前库存, 最低库存, 创建时间)
        SELECT 
          编号,
          0 as 当前库存,
          0 as 最低库存,
          CURRENT_TIMESTAMP
        FROM 药品信息
      `);
    }

    // 3. 创建基本索引
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_药品库存_药品编号 ON 药品库存(药品编号)',
      'CREATE INDEX IF NOT EXISTS idx_药品批次表_药品编号 ON 药品批次表(药品编号)',
      'CREATE INDEX IF NOT EXISTS idx_药品批次表_批次号 ON 药品批次表(批次号)',
      'CREATE INDEX IF NOT EXISTS idx_药品批次表_有效期 ON 药品批次表(有效期)',
      'CREATE INDEX IF NOT EXISTS idx_药品批次表_状态 ON 药品批次表(状态)'
    ];

    for (const indexSql of indexes) {
      await run(indexSql);
    }

    // 4. 验证修复结果
    const finalTableInfo = await query(`PRAGMA table_info(药品批次表)`);
    const stockTableInfo = await query(`PRAGMA table_info(药品库存)`);
    
    console.log('修复后药品批次表结构:', finalTableInfo);
    console.log('药品库存表结构:', stockTableInfo);

    return NextResponse.json({
      success: true,
      message: '药品批次表结构修复成功',
      data: {
        batch_table_structure: finalTableInfo,
        stock_table_structure: stockTableInfo
      }
    });

  } catch (error) {
    console.error('修复药品批次表结构失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '修复药品批次表结构失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 获取当前表结构状态
export async function GET() {
  try {
    const batchTableInfo = await query(`PRAGMA table_info(药品批次表)`);
    const stockTableInfo = await query(`PRAGMA table_info(药品库存)`);
    
    const requiredBatchFields = ['当前数量', '入库数量', '入库成本价', '状态'];
    const missingBatchFields = requiredBatchFields.filter(field => 
      !batchTableInfo.some((col: any) => col.name === field)
    );

    return NextResponse.json({
      success: true,
      data: {
        batch_table_exists: batchTableInfo.length > 0,
        stock_table_exists: stockTableInfo.length > 0,
        batch_table_structure: batchTableInfo,
        stock_table_structure: stockTableInfo,
        missing_batch_fields: missingBatchFields,
        needs_fix: missingBatchFields.length > 0
      }
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        message: '获取表结构状态失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
