import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';
import { ensureSettingsTable } from '@/lib/db-init';

// 前端属性名到数据库设置名称的映射
const SETTING_NAME_MAPPING: Record<string, string> = {
  storeName: '药店名称',
  orderNumberDigits: '订单编号位数',
  receiptSize: '小票尺寸',
  mashangfangxinAppkey: '码上放心开放平台AppKey',
  mashangfangxinAppsecret: '码上放心开放平台AppSecret',
  mashangfangxinUrl: '码上放心开放平台Url',
  mashangfangxinRefEntId: '码上放心开放平台RefEntId'
};

// 数据库设置名称到前端属性名的反向映射
const REVERSE_SETTING_NAME_MAPPING: Record<string, string> = Object.fromEntries(
  Object.entries(SETTING_NAME_MAPPING).map(([key, value]) => [value, key])
);

// 获取系统设置
export async function GET() {
  try {
    // 确保"系统设置"表存在
    await ensureSettingsTable();

    const settings = await query('SELECT 设置名称 as setting_name, 设置值 as setting_value, 描述 as description FROM 系统设置');

    // 将设置转换为键值对对象，使用前端属性名
    const settingsObject: Record<string, string> = {};
    settings.forEach((setting: any) => {
      // 查找对应的前端属性名，如果没有找到则使用原名称
      const frontendKey = REVERSE_SETTING_NAME_MAPPING[setting.setting_name] || setting.setting_name;
      settingsObject[frontendKey] = setting.setting_value;
    });

    return NextResponse.json({
      success: true,
      data: settingsObject
    });
  } catch (error) {
    console.error('获取系统设置失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取系统设置失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 更新系统设置
export async function POST(request: NextRequest) {
  try {
    // 确保"系统设置"表存在
    await ensureSettingsTable();

    const data = await request.json();

    // 验证必要的设置项
    if (!data.storeName) {
      return NextResponse.json(
        { success: false, message: '药店名称不能为空' },
        { status: 400 }
      );
    }

    // 验证订单编号位数
    const orderNumberDigits = parseInt(data.orderNumberDigits);
    if (isNaN(orderNumberDigits) || orderNumberDigits < 4 || orderNumberDigits > 10) {
      return NextResponse.json(
        { success: false, message: '订单编号位数必须在4-10之间' },
        { status: 400 }
      );
    }

    // 更新或插入每个设置项
    for (const [frontendKey, value] of Object.entries(data)) {
      // 跳过空值
      if (value === null || value === undefined) continue;

      // 将前端属性名转换为数据库设置名称
      const dbSettingName = SETTING_NAME_MAPPING[frontendKey] || frontendKey;

      // 检查设置项是否存在
      const existingSetting = await query(
        'SELECT 设置名称 FROM 系统设置 WHERE 设置名称 = ?',
        [dbSettingName]
      );

      if (existingSetting && existingSetting.length > 0) {
        // 更新现有设置
        await run(
          'UPDATE 系统设置 SET 设置值 = ?, 更新时间 = CURRENT_TIMESTAMP WHERE 设置名称 = ?',
          [value, dbSettingName]
        );
      } else {
        // 插入新设置
        await run(
          'INSERT INTO 系统设置 (设置名称, 设置值) VALUES (?, ?)',
          [dbSettingName, value]
        );
      }
    }

    return NextResponse.json({
      success: true,
      message: '系统设置已更新'
    });
  } catch (error) {
    console.error('更新系统设置失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '更新系统设置失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 重置系统设置为默认值
export async function DELETE() {
  try {
    // 确保"系统设置"表存在
    await ensureSettingsTable();

    // 默认设置（使用前端属性名）
    const defaultSettings = {
      storeName: '药店零售管理系统',
      orderNumberDigits: '4',
      receiptSize: '80mm',
      mashangfangxinAppkey: '',
      mashangfangxinAppsecret: '',
      mashangfangxinUrl: 'http://gw.api.taobao.com/router/rest',
      mashangfangxinRefEntId: ''
    };

    // 更新每个设置项为默认值
    for (const [frontendKey, value] of Object.entries(defaultSettings)) {
      const dbSettingName = SETTING_NAME_MAPPING[frontendKey] || frontendKey;
      await run(
        'UPDATE 系统设置 SET 设置值 = ?, 更新时间 = CURRENT_TIMESTAMP WHERE 设置名称 = ?',
        [value, dbSettingName]
      );
    }

    return NextResponse.json({
      success: true,
      message: '系统设置已重置为默认值',
      data: defaultSettings
    });
  } catch (error) {
    console.error('重置系统设置失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '重置系统设置失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
