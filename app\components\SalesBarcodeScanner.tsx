'use client';

import { useState, useEffect, useRef } from 'react';
import { Medicine } from '@/types/medicine';

interface SalesBarcodeScannerProps {
  isActive: boolean;
  onAddMedicine: (medicine: Medicine, quantity: number) => void;
}

/**
 * 销售单扫码添加药品组件
 * 监听键盘输入，捕获扫码枪输入的条码数据，自动添加到销售单
 */
export default function SalesBarcodeScanner({ isActive, onAddMedicine }: SalesBarcodeScannerProps) {
  const [barcode, setBarcode] = useState<string>('');
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [scanStatus, setScanStatus] = useState<'idle' | 'scanning' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState<string>('');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 处理键盘输入
  const handleKeyDown = (e: KeyboardEvent) => {
    // 如果组件不处于激活状态，不处理键盘输入
    if (!isActive) return;

    // 如果按下的是Enter键，表示扫码结束
    if (e.key === 'Enter') {
      if (barcode.length > 0) {
        processBarcode(barcode);
        setBarcode('');
      }
      setIsScanning(false);
      return;
    }

    // 如果是数字或字母，添加到条码中
    if (/^[a-zA-Z0-9]$/.test(e.key)) {
      setIsScanning(true);
      setBarcode(prev => prev + e.key);
      setScanStatus('scanning');

      // 重置超时计时器
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // 设置新的超时计时器，如果500ms内没有新的输入，认为扫码结束
      timeoutRef.current = setTimeout(() => {
        if (barcode.length > 0) {
          processBarcode(barcode + e.key);
          setBarcode('');
        }
        setIsScanning(false);
      }, 500);
    }
  };

  // 处理条码
  const processBarcode = async (code: string) => {
    try {
      setScanStatus('scanning');
      setStatusMessage(`正在查找条码: ${code}`);

      // 先尝试通过条形码查找药品
      const response = await fetch(`/api/products/barcode?code=${encodeURIComponent(code)}`);
      const data = await response.json();

      if (data.success && data.data) {
        // 找到药品，添加到订单
        onAddMedicine(data.data, 1);
        setScanStatus('success');
        setStatusMessage(`已添加: ${data.data.name}`);

        // 3秒后清除状态消息
        setTimeout(() => {
          setScanStatus('idle');
          setStatusMessage('');
        }, 3000);
      } else {
        // 如果通过条形码没找到，尝试通过药品追溯码查找
        try {
          const traceResponse = await fetch('/api/drug-trace', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ code }),
          });

          const traceData = await traceResponse.json();

          if (traceData.success && traceData.data) {
            // 找到药品，添加到订单
            // 注意：这里需要将药品追溯信息转换为Medicine格式
            // 这部分逻辑可能需要根据实际API返回格式调整
            const medicine = convertTraceDataToMedicine(traceData.data, code);
            if (medicine) {
              onAddMedicine(medicine, 1);
              setScanStatus('success');
              setStatusMessage(`已添加: ${medicine.name}`);

              // 3秒后清除状态消息
              setTimeout(() => {
                setScanStatus('idle');
                setStatusMessage('');
              }, 3000);
            } else {
              throw new Error('无法解析药品信息');
            }
          } else {
            throw new Error(traceData.message || '未找到药品');
          }
        } catch (traceError) {
          setScanStatus('error');
          setStatusMessage(`未找到药品: ${code}`);
          console.error('药品追溯码查询失败:', traceError);

          // 3秒后清除错误消息
          setTimeout(() => {
            setScanStatus('idle');
            setStatusMessage('');
          }, 3000);
        }
      }
    } catch (error) {
      setScanStatus('error');
      setStatusMessage(`查询失败: ${error instanceof Error ? error.message : '未知错误'}`);
      console.error('条码处理失败:', error);

      // 3秒后清除错误消息
      setTimeout(() => {
        setScanStatus('idle');
        setStatusMessage('');
      }, 3000);
    }
  };

  // 将药品追溯信息转换为Medicine格式
  const convertTraceDataToMedicine = (traceData: any, scannedCode?: string): Medicine | null => {
    try {
      // 这里的转换逻辑需要根据实际API返回格式调整
      if (!traceData) return null;

      // 尝试从新的API响应格式中提取药品信息
      if (traceData.result?.models?.code_full_info_dto?.[0]) {
        const codeFullInfo = traceData.result.models.code_full_info_dto[0];
        const drugEntBase = codeFullInfo.drug_ent_base_d_t_o || {};

        return {
          id: 0, // 新药品ID为0
          name: drugEntBase.drug_name || '',
          specification: drugEntBase.spec || '',
          manufacturer: codeFullInfo.p_user_ent_d_t_o?.ent_name || '',
          price: 0, // 价格需要手动设置
          stock_quantity: 0, // 库存需要手动设置
          category_name: '', // 分类需要手动设置
          drug_identification_code: scannedCode ? scannedCode.substring(0, 7) : '', // 提取前7位作为药品标识码
          trace_code: scannedCode || ''
        };
      }

      // 旧的API响应格式
      return {
        id: 0,
        name: traceData.drug_name || '',
        specification: traceData.specification || '',
        manufacturer: traceData.manufacturer || '',
        price: 0,
        stock_quantity: 0,
        category_name: '',
        barcode: traceData.barcode || '',
        trace_code: scannedCode || ''
      };
    } catch (error) {
      console.error('转换药品追溯信息失败:', error);
      return null;
    }
  };

  // 组件挂载时添加键盘事件监听
  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown);

    // 组件卸载时移除键盘事件监听
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [barcode, isActive]);

  // 如果没有扫描状态，不显示任何内容
  if (scanStatus === 'idle') return null;

  return (
    <div className="fixed top-4 right-4 z-40">
      <div className={`rounded-lg shadow-lg p-4 max-w-xs w-full ${
        scanStatus === 'scanning' ? 'bg-blue-50 border border-blue-200' :
        scanStatus === 'success' ? 'bg-green-50 border border-green-200' :
        'bg-red-50 border border-red-200'
      }`}>
        <div className="flex items-center">
          {scanStatus === 'scanning' && (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-700 mr-2"></div>
          )}
          {scanStatus === 'success' && (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          )}
          {scanStatus === 'error' && (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-600 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
          )}
          <span className={`text-sm ${
            scanStatus === 'scanning' ? 'text-blue-700' :
            scanStatus === 'success' ? 'text-green-700' :
            'text-red-700'
          }`}>
            {statusMessage}
          </span>
        </div>
      </div>
    </div>
  );
}
