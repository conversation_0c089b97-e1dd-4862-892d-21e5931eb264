import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

/**
 * 库存管理数据库结构重构迁移
 * 
 * 此迁移脚本将：
 * 1. 创建新的库存管理表结构
 * 2. 迁移现有的批次和库存记录数据
 * 3. 创建库存汇总和过期预警视图
 * 4. 建立必要的索引和触发器
 */

export async function POST(request: NextRequest) {
  try {
    console.log('开始执行库存管理数据库结构重构迁移...');

    // 开始事务
    await run('BEGIN TRANSACTION');

    // 1. 创建药品库存主表
    console.log('创建药品库存主表...');
    await run(`
      CREATE TABLE IF NOT EXISTS 药品库存 (
        编号 INTEGER PRIMARY KEY AUTOINCREMENT,
        药品编号 INTEGER NOT NULL UNIQUE,
        当前库存 INTEGER NOT NULL DEFAULT 0,
        最低库存 INTEGER DEFAULT 0,
        最高库存 INTEGER DEFAULT 0,
        平均成本价 DECIMAL(10,2) DEFAULT 0,
        最后入库时间 DATETIME,
        最后出库时间 DATETIME,
        创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
        更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 2. 检查并重构药品批次表
    console.log('检查药品批次表结构...');
    const batchTableInfo = await query(`PRAGMA table_info(药品批次表)`);
    
    if (!batchTableInfo || batchTableInfo.length === 0) {
      // 创建新的药品批次表
      console.log('创建药品批次表...');
      await run(`
        CREATE TABLE 药品批次表 (
          编号 INTEGER PRIMARY KEY AUTOINCREMENT,
          药品编号 INTEGER NOT NULL,
          批次号 TEXT NOT NULL,
          生产日期 DATE,
          有效期 DATE NOT NULL,
          入库数量 INTEGER NOT NULL DEFAULT 0,
          当前数量 INTEGER NOT NULL DEFAULT 0,
          供应商编号 INTEGER,
          入库成本价 DECIMAL(10,2),
          状态 TEXT DEFAULT 'active' CHECK (状态 IN ('active', 'expired', 'depleted', 'locked')),
          创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
          更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
          备注 TEXT
        )
      `);
      
      // 创建唯一约束
      await run(`CREATE UNIQUE INDEX idx_药品批次表_唯一 ON 药品批次表(药品编号, 批次号)`);
    } else {
      // 检查是否需要添加新字段
      const hasCurrentQuantity = batchTableInfo.some((col: any) => col.name === '当前数量');
      const hasInboundQuantity = batchTableInfo.some((col: any) => col.name === '入库数量');
      const hasInboundCostPrice = batchTableInfo.some((col: any) => col.name === '入库成本价');
      
      if (!hasCurrentQuantity) {
        await run(`ALTER TABLE 药品批次表 ADD COLUMN 当前数量 INTEGER NOT NULL DEFAULT 0`);
        // 将现有的剩余数量数据迁移到当前数量
        await run(`UPDATE 药品批次表 SET 当前数量 = COALESCE(剩余数量, 数量, 0)`);
      }
      
      if (!hasInboundQuantity) {
        await run(`ALTER TABLE 药品批次表 ADD COLUMN 入库数量 INTEGER NOT NULL DEFAULT 0`);
        // 将现有的数量数据迁移到入库数量
        await run(`UPDATE 药品批次表 SET 入库数量 = COALESCE(数量, 0)`);
      }
      
      if (!hasInboundCostPrice) {
        await run(`ALTER TABLE 药品批次表 ADD COLUMN 入库成本价 DECIMAL(10,2)`);
        // 将现有的成本价数据迁移到入库成本价
        await run(`UPDATE 药品批次表 SET 入库成本价 = 成本价`);
      }
    }

    // 3. 重构库存变动记录表
    console.log('重构库存变动记录表...');
    
    // 检查现有库存记录表
    const stockRecordExists = await query(`SELECT name FROM sqlite_master WHERE type='table' AND name='库存记录'`);
    
    if (stockRecordExists && stockRecordExists.length > 0) {
      // 备份现有表
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      await run(`CREATE TABLE 库存记录_备份_${timestamp} AS SELECT * FROM 库存记录`);
      
      // 删除旧表
      await run(`DROP TABLE 库存记录`);
    }
    
    // 创建新的库存变动记录表
    await run(`
      CREATE TABLE 库存变动记录 (
        编号 INTEGER PRIMARY KEY AUTOINCREMENT,
        药品编号 INTEGER NOT NULL,
        批次编号 INTEGER,
        操作类型 TEXT NOT NULL CHECK (操作类型 IN ('入库', '出库', '盘点', '调整', '过期处理')),
        变动数量 INTEGER NOT NULL,
        变动前数量 INTEGER NOT NULL DEFAULT 0,
        变动后数量 INTEGER NOT NULL DEFAULT 0,
        单价 DECIMAL(10,2),
        总金额 DECIMAL(10,2),
        供应商编号 INTEGER,
        关联单据类型 TEXT CHECK (关联单据类型 IN ('采购单', '销售单', '盘点单', '调整单', '报损单')),
        关联单据编号 TEXT,
        操作人 TEXT,
        操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
        备注 TEXT,
        码上放心单据号 TEXT,
        码上放心上传状态 TEXT CHECK (码上放心上传状态 IN ('pending', 'success', 'failed')),
        码上放心上传时间 DATETIME,
        码上放心响应 TEXT
      )
    `);

    // 4. 创建库存盘点表
    console.log('创建库存盘点表...');
    await run(`
      CREATE TABLE IF NOT EXISTS 库存盘点 (
        编号 INTEGER PRIMARY KEY AUTOINCREMENT,
        盘点编号 TEXT UNIQUE NOT NULL,
        盘点名称 TEXT NOT NULL,
        盘点类型 TEXT CHECK (盘点类型 IN ('全盘', '抽盘', '动态盘点')) DEFAULT '全盘',
        盘点状态 TEXT CHECK (盘点状态 IN ('进行中', '已完成', '已取消')) DEFAULT '进行中',
        开始时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
        完成时间 DATETIME,
        盘点人 TEXT,
        审核人 TEXT,
        备注 TEXT,
        创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 5. 创建库存盘点明细表
    console.log('创建库存盘点明细表...');
    await run(`
      CREATE TABLE IF NOT EXISTS 库存盘点明细 (
        编号 INTEGER PRIMARY KEY AUTOINCREMENT,
        盘点编号 INTEGER NOT NULL,
        药品编号 INTEGER NOT NULL,
        批次编号 INTEGER,
        账面数量 INTEGER NOT NULL DEFAULT 0,
        实盘数量 INTEGER,
        差异数量 INTEGER DEFAULT 0,
        差异原因 TEXT,
        盘点状态 TEXT CHECK (盘点状态 IN ('待盘点', '已盘点', '有差异', '已处理')) DEFAULT '待盘点',
        盘点时间 DATETIME,
        备注 TEXT
      )
    `);

    // 6. 创建过期预警配置表
    console.log('创建过期预警配置表...');
    await run(`
      CREATE TABLE IF NOT EXISTS 过期预警配置 (
        编号 INTEGER PRIMARY KEY AUTOINCREMENT,
        药品编号 INTEGER,
        药品分类编号 INTEGER,
        预警天数 INTEGER NOT NULL DEFAULT 30,
        预警级别 TEXT CHECK (预警级别 IN ('低', '中', '高')) DEFAULT '中',
        是否启用 BOOLEAN DEFAULT 1,
        创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
        更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    console.log('创建索引...');
    // 7. 创建索引
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_药品库存_药品编号 ON 药品库存(药品编号)',
      'CREATE INDEX IF NOT EXISTS idx_药品库存_更新时间 ON 药品库存(更新时间)',
      'CREATE INDEX IF NOT EXISTS idx_药品批次表_药品编号 ON 药品批次表(药品编号)',
      'CREATE INDEX IF NOT EXISTS idx_药品批次表_批次号 ON 药品批次表(批次号)',
      'CREATE INDEX IF NOT EXISTS idx_药品批次表_有效期 ON 药品批次表(有效期)',
      'CREATE INDEX IF NOT EXISTS idx_药品批次表_状态 ON 药品批次表(状态)',
      'CREATE INDEX IF NOT EXISTS idx_库存变动记录_药品编号 ON 库存变动记录(药品编号)',
      'CREATE INDEX IF NOT EXISTS idx_库存变动记录_批次编号 ON 库存变动记录(批次编号)',
      'CREATE INDEX IF NOT EXISTS idx_库存变动记录_操作时间 ON 库存变动记录(操作时间)',
      'CREATE INDEX IF NOT EXISTS idx_库存变动记录_操作类型 ON 库存变动记录(操作类型)',
      'CREATE INDEX IF NOT EXISTS idx_库存盘点明细_盘点编号 ON 库存盘点明细(盘点编号)',
      'CREATE INDEX IF NOT EXISTS idx_库存盘点明细_药品编号 ON 库存盘点明细(药品编号)'
    ];

    for (const indexSql of indexes) {
      await run(indexSql);
    }

    console.log('迁移现有数据...');
    // 8. 迁移现有数据到药品库存表
    await run(`
      INSERT OR IGNORE INTO 药品库存 (药品编号, 当前库存, 最低库存, 创建时间)
      SELECT 
        p.编号,
        COALESCE(SUM(b.当前数量), 0) as 当前库存,
        0 as 最低库存,
        CURRENT_TIMESTAMP
      FROM 药品信息 p
      LEFT JOIN 药品批次表 b ON p.编号 = b.药品编号 AND b.状态 = 'active'
      GROUP BY p.编号
    `);

    // 提交事务
    await run('COMMIT');

    console.log('库存管理数据库结构重构迁移完成');

    return NextResponse.json({
      success: true,
      message: '库存管理数据库结构重构成功',
      data: {
        migrated: true,
        tables_created: [
          '药品库存',
          '库存变动记录',
          '库存盘点',
          '库存盘点明细',
          '过期预警配置'
        ],
        tables_updated: ['药品批次表'],
        indexes_created: 12
      }
    });

  } catch (error) {
    console.error('库存管理数据库结构重构失败:', error);
    
    // 回滚事务
    try {
      await run('ROLLBACK');
    } catch (rollbackError) {
      console.error('回滚失败:', rollbackError);
    }

    return NextResponse.json(
      {
        success: false,
        message: '库存管理数据库结构重构失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 获取迁移状态
export async function GET() {
  try {
    const tables = ['药品库存', '库存变动记录', '库存盘点', '库存盘点明细', '过期预警配置'];
    const tableStatus = {};

    for (const tableName of tables) {
      const tableExists = await query(`SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}'`);
      tableStatus[tableName] = tableExists && tableExists.length > 0;
    }

    // 检查药品库存表的记录数
    let stockRecordCount = 0;
    if (tableStatus['药品库存']) {
      const countResult = await query(`SELECT COUNT(*) as count FROM 药品库存`);
      stockRecordCount = countResult[0]?.count || 0;
    }

    return NextResponse.json({
      success: true,
      data: {
        tables: tableStatus,
        stockRecordCount,
        migrationNeeded: !Object.values(tableStatus).every(exists => exists)
      }
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        message: '获取迁移状态失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
