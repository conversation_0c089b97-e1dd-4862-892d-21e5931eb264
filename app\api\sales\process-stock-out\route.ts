import { NextRequest, NextResponse } from 'next/server';
import { query, get, run } from '@/lib/db';

/**
 * 处理销售订单的药品出库操作
 * POST /api/sales/process-stock-out
 */
export async function POST(request: NextRequest) {
  try {
    const { orderId, items } = await request.json();

    if (!orderId || !items || !Array.isArray(items)) {
      return NextResponse.json({
        success: false,
        message: '参数错误：需要提供订单ID和商品列表'
      }, { status: 400 });
    }

    console.log('处理销售订单出库:', { orderId, itemsCount: items.length });

    // 开始事务
    await run('BEGIN TRANSACTION');

    try {
      const stockOutResults = [];

      for (const item of items) {
        const {
          productId,
          quantity,
          traceCode,
          batchId,
          batchInfo
        } = item;

        if (!productId || !quantity) {
          continue; // 跳过无效项目
        }

        // 1. 获取药品信息
        const product = await get(
          'SELECT * FROM 药品信息 WHERE 编号 = ?',
          [productId]
        );

        if (!product) {
          throw new Error(`药品ID ${productId} 不存在`);
        }

        const currentStock = product.库存数量 || 0;
        if (currentStock < quantity) {
          throw new Error(`药品 ${product.名称} 库存不足，当前库存：${currentStock}，需要：${quantity}`);
        }

        const newStock = currentStock - quantity;

        // 2. 创建库存记录（出库）
        const stockOutResult = await run(
          `INSERT INTO 库存记录 (
            药品编号, 操作类型, 数量变化, 操作前库存, 操作后库存,
            批次号, 有效期, 操作人, 备注, 操作时间
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            productId,
            '出库',
            -quantity, // 出库为负数
            currentStock,
            newStock,
            batchInfo?.batchNo || null,
            batchInfo?.expireDate || null,
            '系统操作员',
            `销售订单出库 - 订单号：${orderId}`,
            new Date().toISOString()
          ]
        );

        const inventoryId = stockOutResult.lastID;

        // 3. 更新药品库存
        await run(
          'UPDATE 药品信息 SET 库存数量 = ? WHERE 编号 = ?',
          [newStock, productId]
        );

        // 4. 处理批次出库（如果有批次信息）
        if (batchId) {
          // 更新批次剩余数量
          await run(
            'UPDATE 药品批次表 SET 剩余数量 = 剩余数量 - ?, 更新时间 = CURRENT_TIMESTAMP WHERE 编号 = ?',
            [quantity, batchId]
          );

          // 检查批次是否用完
          const updatedBatch = await get(
            'SELECT 剩余数量 FROM 药品批次表 WHERE 编号 = ?',
            [batchId]
          );

          if (updatedBatch && updatedBatch.剩余数量 <= 0) {
            await run(
              'UPDATE 药品批次表 SET 状态 = "depleted", 更新时间 = CURRENT_TIMESTAMP WHERE 编号 = ?',
              [batchId]
            );
          }
        } else if (batchInfo?.batchNo) {
          // 如果没有本地批次ID但有批次信息，尝试FIFO出库
          const availableBatches = await query(
            `SELECT 编号, 批次号, 剩余数量, 有效期
             FROM 药品批次表
             WHERE 药品编号 = ? AND 状态 = 'active' AND 剩余数量 > 0
             ORDER BY 有效期 ASC, 创建时间 ASC`,
            [productId]
          );

          let remainingQuantity = quantity;
          for (const batch of availableBatches) {
            if (remainingQuantity <= 0) break;

            const batchQuantityToUse = Math.min(remainingQuantity, batch.剩余数量);

            await run(
              'UPDATE 药品批次表 SET 剩余数量 = 剩余数量 - ?, 更新时间 = CURRENT_TIMESTAMP WHERE 编号 = ?',
              [batchQuantityToUse, batch.编号]
            );

            if (batch.剩余数量 - batchQuantityToUse <= 0) {
              await run(
                'UPDATE 药品批次表 SET 状态 = "depleted", 更新时间 = CURRENT_TIMESTAMP WHERE 编号 = ?',
                [batch.编号]
              );
            }

            remainingQuantity -= batchQuantityToUse;
          }
        }

        // 5. 记录追溯码信息（如果有）
        if (traceCode) {
          await run(
            `INSERT INTO 药品追溯码记录 (
              库存记录编号, 销售订单编号, 药品编号, 追溯码, 操作类型,
              批次号, 有效期, 生产厂家, 药品名称, 规格, 备注
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
            [
              inventoryId,
              orderId,
              productId,
              traceCode,
              '销售',
              batchInfo?.batchNo || null,
              batchInfo?.expireDate || null,
              batchInfo?.manufacturer || null,
              batchInfo?.drugName || product.名称,
              batchInfo?.specification || product.规格,
              `销售订单出库 - 订单号：${orderId}`
            ]
          );
        }

        stockOutResults.push({
          productId,
          productName: product.名称,
          quantity,
          previousStock: currentStock,
          newStock,
          traceCode: traceCode || null,
          batchInfo: batchInfo || null,
          inventoryId
        });
      }

      // 提交事务
      await run('COMMIT');

      return NextResponse.json({
        success: true,
        message: '销售出库处理成功',
        data: {
          orderId,
          stockOutResults,
          totalItems: stockOutResults.length
        }
      });

    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('处理销售出库失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '处理销售出库失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
