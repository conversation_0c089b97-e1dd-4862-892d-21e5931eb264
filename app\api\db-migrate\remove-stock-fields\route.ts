import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

/**
 * 数据库迁移：从药品信息表中移除库存相关字段
 * 
 * 此迁移脚本将：
 * 1. 备份当前药品信息表
 * 2. 创建新的药品信息表（不包含库存字段）
 * 3. 迁移现有数据（排除库存字段）
 * 4. 删除旧表
 * 5. 重建索引和外键约束
 */

export async function POST(request: NextRequest) {
  try {
    console.log('开始执行药品信息表库存字段移除迁移...');

    // 1. 检查当前表结构
    const tableInfo = await query(`PRAGMA table_info(药品信息)`);
    console.log('当前药品信息表结构:', tableInfo);

    // 检查是否存在库存相关字段
    const hasStockQuantity = tableInfo.some((col: any) => col.name === '库存数量');
    const hasMinStock = tableInfo.some((col: any) => col.name === '最低库存');

    if (!hasStockQuantity && !hasMinStock) {
      return NextResponse.json({
        success: true,
        message: '药品信息表已经不包含库存字段，无需迁移',
        data: { migrated: false }
      });
    }

    // 2. 创建备份表
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupTableName = `药品信息_备份_${timestamp}`;
    
    await run(`CREATE TABLE "${backupTableName}" AS SELECT * FROM 药品信息`);
    console.log(`备份表创建成功: ${backupTableName}`);

    // 3. 创建新的药品信息表（不包含库存字段）
    await run(`
      CREATE TABLE 药品信息_新 (
        编号 INTEGER PRIMARY KEY AUTOINCREMENT,
        名称 TEXT NOT NULL,
        通用名 TEXT,
        描述 TEXT,
        药品标识码 TEXT,
        分类编号 INTEGER,
        供应商编号 INTEGER,
        生产厂家 TEXT,
        批准文号 TEXT,
        规格 TEXT,
        剂型 TEXT,
        售价 DECIMAL(10,2) DEFAULT 0,
        成本价 DECIMAL(10,2) DEFAULT 0,
        是否处方药 BOOLEAN DEFAULT 0,
        是否医保 BOOLEAN DEFAULT 0,
        储存条件 TEXT DEFAULT '常温',
        状态 TEXT DEFAULT 'active',
        创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (分类编号) REFERENCES 药品分类(编号),
        FOREIGN KEY (供应商编号) REFERENCES 供应商信息表(编号)
      )
    `);
    console.log('新药品信息表创建成功');

    // 4. 迁移数据（排除库存字段）
    await run(`
      INSERT INTO 药品信息_新 (
        编号, 名称, 通用名, 描述, 药品标识码, 分类编号, 供应商编号,
        生产厂家, 批准文号, 规格, 剂型, 售价, 成本价,
        是否处方药, 是否医保, 储存条件, 状态, 创建时间, 更新时间
      )
      SELECT 
        编号, 名称, 通用名, 描述, 药品标识码, 分类编号, 供应商编号,
        生产厂家, 批准文号, 规格, 剂型, 售价, 成本价,
        是否处方药, 是否医保, 储存条件, 状态, 创建时间, 更新时间
      FROM 药品信息
    `);
    console.log('数据迁移完成');

    // 5. 删除原表
    await run(`DROP TABLE 药品信息`);
    console.log('原药品信息表已删除');

    // 6. 重命名新表
    await run(`ALTER TABLE 药品信息_新 RENAME TO 药品信息`);
    console.log('新表重命名完成');

    // 7. 重建索引
    await run(`CREATE INDEX IF NOT EXISTS idx_药品信息_名称 ON 药品信息(名称)`);
    await run(`CREATE INDEX IF NOT EXISTS idx_药品信息_药品标识码 ON 药品信息(药品标识码)`);
    await run(`CREATE INDEX IF NOT EXISTS idx_药品信息_分类编号 ON 药品信息(分类编号)`);
    await run(`CREATE INDEX IF NOT EXISTS idx_药品信息_供应商编号 ON 药品信息(供应商编号)`);
    console.log('索引重建完成');

    // 8. 验证迁移结果
    const newTableInfo = await query(`PRAGMA table_info(药品信息)`);
    const recordCount = await query(`SELECT COUNT(*) as count FROM 药品信息`);
    
    console.log('迁移后表结构:', newTableInfo);
    console.log('迁移后记录数:', recordCount);

    return NextResponse.json({
      success: true,
      message: '药品信息表库存字段移除迁移完成',
      data: {
        migrated: true,
        backupTable: backupTableName,
        recordCount: recordCount[0]?.count || 0,
        removedFields: ['库存数量', '最低库存']
      }
    });

  } catch (error) {
    console.error('数据库迁移失败:', error);
    
    // 尝试回滚：如果新表已创建但迁移失败，删除新表
    try {
      await run(`DROP TABLE IF EXISTS 药品信息_新`);
    } catch (rollbackError) {
      console.error('回滚失败:', rollbackError);
    }

    return NextResponse.json(
      {
        success: false,
        message: '数据库迁移失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 获取迁移状态
export async function GET() {
  try {
    const tableInfo = await query(`PRAGMA table_info(药品信息)`);
    const hasStockQuantity = tableInfo.some((col: any) => col.name === '库存数量');
    const hasMinStock = tableInfo.some((col: any) => col.name === '最低库存');

    return NextResponse.json({
      success: true,
      data: {
        hasStockFields: hasStockQuantity || hasMinStock,
        stockQuantityExists: hasStockQuantity,
        minStockExists: hasMinStock,
        tableStructure: tableInfo
      }
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        message: '获取表结构信息失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
