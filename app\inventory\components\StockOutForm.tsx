'use client';

import { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';

interface Product {
  id: number;
  name: string;
  generic_name: string;
  barcode: string;
  stock_quantity: number;
  category_name: string;
  manufacturer: string;
  specification: string;
}

interface ApiResponse {
  success: boolean;
  data?: Product[];
  message?: string;
}

interface StockOutFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
}

interface StockOutFormData {
  product_id: number;
  quantity: number;
  reason: string;
  stock_out_date: string;
  notes: string;
  batch_number: string;
  batch_type: string;
}

export default function StockOutForm({ isOpen, onClose, onSubmit }: StockOutFormProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [formData, setFormData] = useState<StockOutFormData>({
    product_id: 0,
    quantity: 1,
    reason: 'sale',
    stock_out_date: new Date().toISOString().split('T')[0],
    notes: '',
    batch_number: '',
    batch_type: 'SO' // 默认为销售出库
  });

  // 出库类型到批次类型的映射
  const reasonToBatchTypeMap: Record<string, string> = {
    'sale': 'SO',      // 销售出库
    'return': 'RT',    // 退货出库
    'expired': 'EX',   // 过期处理
    'damaged': 'DM',   // 破损处理
    'other': 'OT'      // 其他原因
  };

  useEffect(() => {
    if (isOpen) {
      fetchProducts();
      // 重置表单数据
      setFormData({
        product_id: 0,
        quantity: 1,
        reason: 'sale',
        stock_out_date: new Date().toISOString().split('T')[0],
        notes: '',
        batch_number: '',
        batch_type: 'SO'
      });
    }
  }, [isOpen]);

  // 自动生成批次号
  const generateBatchNumber = async (batchType: string, productId: number) => {
    if (!productId) return '';

    try {
      const response = await fetch(`/api/inventory/batch-number?type=${batchType}&product_id=${productId}`);
      const data = await response.json();

      if (data.success) {
        return data.data.batchNumber;
      }
    } catch (error) {
      console.error('生成批次号失败:', error);
    }

    // 如果API失败，使用简单的时间戳批次号
    const timestamp = Date.now();
    return `${batchType}-${timestamp}`;
  };

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/products');
      const data = await response.json() as ApiResponse;
      if (data.success && data.data) {
        // 只显示有库存的产品
        const productsWithStock = data.data.filter((p: Product) => p.stock_quantity > 0);
        setProducts(productsWithStock);
      }
    } catch (error) {
      console.error('获取药品数据失败:', error);
    } finally {
      setLoading(false);
    }
  };
  
  // 修改为单独处理产品选择
  const handleProductChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const productId = parseInt((e.target as HTMLSelectElement).value);

    if (!isNaN(productId)) {
      const product = products.find(p => p.id === productId);
      setSelectedProduct(product || null);

      // 生成批次号
      const batchNumber = await generateBatchNumber(formData.batch_type, productId);

      setFormData(prev => ({
        ...prev,
        product_id: productId,
        quantity: prev.quantity > (product?.stock_quantity || 0) ? product?.stock_quantity || 1 : prev.quantity,
        batch_number: batchNumber
      }));
    }
  };
  
  // 修改为单独处理数量变更
  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt((e.target as HTMLInputElement).value) || 0;
    setFormData(prev => ({
      ...prev,
      quantity: value
    }));
  };
  
  // 修改为单独处理原因选择
  const handleReasonChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const reason = (e.target as HTMLSelectElement).value;
    const batchType = reasonToBatchTypeMap[reason] || 'OT';

    // 如果已选择产品，重新生成批次号
    let batchNumber = '';
    if (formData.product_id) {
      batchNumber = await generateBatchNumber(batchType, formData.product_id);
    }

    setFormData(prev => ({
      ...prev,
      reason: reason,
      batch_type: batchType,
      batch_number: batchNumber
    }));
  };
  
  // 修改为单独处理日期变更
  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      stock_out_date: (e.target as HTMLInputElement).value
    }));
  };
  
  // 修改为单独处理备注变更
  const handleNotesChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      notes: (e.target as HTMLTextAreaElement).value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // 确保有批次号
    if (!formData.batch_number && formData.product_id) {
      console.error('批次号生成失败，请重新选择药品');
      return;
    }

    // 提交包含批次号的数据
    const submitData = {
      ...formData,
      auto_generate_batch: true,
      batch_type: formData.batch_type
    };

    onSubmit(submitData);
  };

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />
      
      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-2xl w-full bg-gray-50 rounded-xl shadow-lg border border-gray-200">
          <div className="p-6">
            <Dialog.Title className="text-xl font-medium leading-6 text-gray-800 mb-6 border-b pb-3">
              药品出库
            </Dialog.Title>

            <form onSubmit={handleSubmit} className="space-y-5">
              <div className="grid grid-cols-2 gap-5">
                <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-600 mb-1">选择药品</label>
                  <select
                    name="product_id"
                    value={formData.product_id || ""}
                    onChange={handleProductChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-gray-50 text-gray-900"
                    required
                  >
                    <option value="">请选择药品</option>
                    {products.map(product => (
                      <option key={product.id} value={product.id}>
                        {product.name} ({product.specification || '规格未知'}) - 当前库存: {product.stock_quantity}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">出库数量</label>
                  <input
                    type="number"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleQuantityChange}
                    min="1"
                    max={selectedProduct?.stock_quantity || 1}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-gray-50 text-gray-900"
                    required
                  />
                  {selectedProduct && (
                    <p className="text-xs text-gray-500 mt-1">
                      最大可出库数量: {selectedProduct.stock_quantity} {selectedProduct.unit}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">出库原因</label>
                  <select
                    name="reason"
                    value={formData.reason}
                    onChange={handleReasonChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-gray-50 text-gray-900"
                    required
                  >
                    <option value="sale">销售出库</option>
                    <option value="return">退货出库</option>
                    <option value="expired">过期处理</option>
                    <option value="damaged">破损处理</option>
                    <option value="other">其他原因</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-600 mb-1">出库日期</label>
                  <input
                    type="date"
                    name="stock_out_date"
                    value={formData.stock_out_date}
                    onChange={handleDateChange}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-gray-50 text-gray-900"
                    required
                  />
                </div>

                <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-600 mb-1">
                    出库批次号
                    <span className="text-xs text-gray-500 ml-1">(系统自动生成)</span>
                  </label>
                  <input
                    type="text"
                    value={formData.batch_number}
                    readOnly
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm bg-gray-100 text-gray-700 font-mono text-sm"
                    placeholder="选择药品和出库原因后自动生成"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    批次号格式：{formData.batch_type}-日期序号，根据出库类型自动生成
                  </p>
                </div>

                <div className="col-span-2">
                  <label className="block text-sm font-medium text-gray-600 mb-1">备注</label>
                  <textarea
                    name="notes"
                    value={formData.notes}
                    onChange={handleNotesChange}
                    rows={3}
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 bg-gray-50 text-gray-900"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-8 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 border border-gray-300 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  取消
                </button>
                <button
                  type="submit"
                  className="px-5 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  disabled={loading}
                >
                  {loading ? '保存中...' : '保存'}
                </button>
              </div>
            </form>
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
} 