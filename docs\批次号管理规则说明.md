# 批次号管理规则说明

## 概述
批次号是药品追溯管理的重要组成部分，用于标识同一生产批次的药品。本文档详细说明了系统中批次号的来源、填写规则和管理策略。

## 批次号来源策略

### 1. 药品追溯码扫描获取（最推荐）
**适用场景：** 有药品追溯码的药品入库
**获取方式：** 通过码上放心平台API自动获取
**数据来源：** `alibaba.alihealth.drugtrace.top.lsyd.query.codedetail` API响应中的 `batch_no` 字段

**示例：**
```json
{
  "code": "84084700665075883011",
  "code_produce_info_d_t_o": {
    "produce_info_list": {
      "produce_info_dto": [
        {
          "batch_no": "240346",
          "expire_date": "20270326",
          "pkg_amount": "1",
          "produce_date_str": "2024-03-27"
        }
      ]
    }
  }
}
```

**优点：**
- 数据准确性高，来源权威
- 自动获取，减少人工输入错误
- 与药品追溯码关联，便于追溯管理

### 2. 系统自动生成（推荐）
**适用场景：**
- 无药品追溯码的药品入库
- 需要统一批次号格式的场景
- 大批量入库操作

**生成规则：** `类型前缀-YYYYMMDD序号`
**支持的批次类型：**
- `PC`: 采购批次 (Purchase)
- `PR`: 生产批次 (Production)
- `RT`: 退货批次 (Return)
- `AD`: 调整批次 (Adjustment)
- `TR`: 调拨批次 (Transfer)
- `QC`: 质检批次 (Quality Control)

**示例：**
- `PC-202506290001` - 2025年6月29日第1个采购批次
- `PR-202506290002` - 2025年6月29日第2个生产批次

**优点：**
- 格式统一，便于管理
- 自动递增序号，避免重复
- 包含日期信息，便于追溯
- 支持多种业务类型

### 3. 手动输入
**适用场景：**
- 需要使用特定批次号的场景
- 历史数据录入
- 特殊业务需求

**输入规则：**
- 必须与药品包装上的批次号完全一致
- 支持字母、数字和常见符号
- 长度限制：1-50个字符
- 不允许包含特殊字符：`<>'"&`

## 批次号验证规则

### 基本验证
1. **非空验证：** 批次号不能为空
2. **长度验证：** 1-50个字符
3. **字符验证：** 只允许字母、数字、连字符、下划线
4. **格式验证：** 不能包含HTML标签或SQL注入字符

### 业务验证
1. **唯一性验证：** 同一药品的同一批次号不能重复入库（除非是补货）
2. **有效期关联：** 批次号必须与有效期匹配
3. **供应商关联：** 同一批次号应来自同一供应商

## 数据库存储

### 库存记录表
```sql
批次号 TEXT, -- 存储批次号信息
有效期 DATE, -- 与批次号关联的有效期
```

### 药品追溯码记录表
```sql
追溯码 TEXT NOT NULL, -- 药品追溯码
批次号 TEXT, -- 从追溯码解析出的批次号（冗余存储，便于查询）
```

## 入库流程中的批次号处理

### 流程图
```
开始入库
    ↓
是否有追溯码？
    ↓ 是
扫描追溯码 → 调用码上放心API → 获取批次号和有效期
    ↓
验证批次号格式
    ↓
保存到数据库
    ↓
完成入库

    ↓ 否（无追溯码）
选择批次号输入方式
    ↓
自动生成？
    ↓ 是
选择批次类型 → 调用批次号生成API → 获取新批次号
    ↓
验证批次号格式
    ↓
保存到数据库
    ↓
完成入库

    ↓ 否（手动输入）
手动输入批次号和有效期
    ↓
验证批次号格式
    ↓
保存到数据库
    ↓
完成入库
```

### 代码实现要点

1. **优先级处理：**
   ```javascript
   // 批次号来源优先级：追溯码 > 自动生成 > 手动输入
   let finalBatchNumber = batch_number;
   let batchNumberSource = 'manual';

   if (auto_generate_batch && !batch_number) {
     const batchResponse = await fetch(`/api/inventory/batch-number?type=${batch_type}&product_id=${product_id}`);
     if (batchResponse.ok) {
       const batchData = await batchResponse.json();
       finalBatchNumber = batchData.data.batchNumber;
       batchNumberSource = 'auto';
     }
   }
   ```

2. **自动生成API调用：**
   ```javascript
   // 生成批次号
   const response = await fetch(`/api/inventory/batch-number?type=PC&product_id=7`);
   const data = await response.json();
   // 返回格式：PC-202506290001

   // 验证批次号
   const validateResponse = await fetch('/api/inventory/batch-number', {
     method: 'POST',
     body: JSON.stringify({ batchNumber: 'PC-202506290001', productId: 7 })
   });
   ```

3. **数据验证：**
   ```javascript
   function validateBatchNumber(batchNumber) {
     if (!batchNumber || batchNumber.trim().length === 0) {
       throw new Error('批次号不能为空');
     }
     if (batchNumber.length > 50) {
       throw new Error('批次号长度不能超过50个字符');
     }
     if (!/^[A-Za-z0-9\-_\u4e00-\u9fa5]+$/.test(batchNumber)) {
       throw new Error('批次号只能包含字母、数字、连字符、下划线和中文字符');
     }
   }
   ```

## 前端界面设计建议

### 入库表单
1. **追溯码扫描区域：**
   - 扫码枪输入框
   - 自动解析并填充批次号、有效期等信息
   - 显示解析状态和结果

2. **批次号输入区域：**
   - 文本输入框（当无追溯码时启用）
   - 实时验证提示
   - 历史批次号下拉建议

3. **信息确认区域：**
   - 显示最终确定的批次号
   - 显示数据来源（追溯码解析/手动输入）
   - 允许手动修改（带警告提示）

## 查询和报表功能

### 批次号查询
- 按批次号查询库存
- 按批次号查询入库记录
- 按批次号查询销售记录

### 批次号报表
- 批次号库存统计
- 即将过期批次号预警
- 批次号流向追溯报表

## 异常处理

### 常见异常情况
1. **追溯码解析失败：** 提示手动输入批次号
2. **批次号重复：** 询问是否为同批次补货
3. **批次号格式错误：** 提供格式修正建议
4. **有效期不匹配：** 提醒检查批次号和有效期的一致性

### 错误处理策略
- 提供友好的错误提示
- 支持数据修正和重新提交
- 记录异常日志便于问题排查

## 最佳实践建议

1. **优先使用追溯码：** 能扫码的药品尽量通过扫码获取批次号
2. **数据校验：** 严格验证批次号格式和业务规则
3. **用户培训：** 培训操作人员正确识别和输入批次号
4. **定期检查：** 定期检查批次号数据的准确性和完整性
5. **备份策略：** 重要的批次号数据要有备份和恢复机制
