'use client';

import React, { useState } from 'react';

interface SimpleBatchNumberInputProps {
  value: string;
  onChange: (value: string, isAuto: boolean) => void;
  productId?: number;
  disabled?: boolean;
  className?: string;
}

const batchTypes = [
  { value: 'PC', label: '采购批次' },
  { value: 'PR', label: '生产批次' },
  { value: 'RT', label: '退货批次' },
  { value: 'AD', label: '调整批次' },
  { value: 'TR', label: '调拨批次' },
  { value: 'QC', label: '质检批次' }
];

export default function SimpleBatchNumberInput({ 
  value, 
  onChange, 
  productId, 
  disabled = false,
  className = ''
}: SimpleBatchNumberInputProps) {
  const [isAutoMode, setIsAutoMode] = useState(true);
  const [selectedBatchType, setSelectedBatchType] = useState('PC');
  const [isGenerating, setIsGenerating] = useState(false);

  // 自动生成批次号
  const generateBatchNumber = async () => {
    if (!productId) {
      console.warn('请先选择药品');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch(`/api/inventory/batch-number?type=${selectedBatchType}&product_id=${productId}`);
      const data = await response.json();
      
      if (data.success) {
        const newBatchNumber = data.data.batchNumber;
        onChange(newBatchNumber, true);
      } else {
        console.error('生成批次号失败：' + data.message);
      }
    } catch (error) {
      console.error('生成批次号失败:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // 切换模式
  const handleModeChange = (autoMode: boolean) => {
    setIsAutoMode(autoMode);
    if (autoMode && productId) {
      generateBatchNumber();
    } else {
      onChange('', false);
    }
  };

  // 手动输入变化
  const handleManualChange = (newValue: string) => {
    onChange(newValue, false);
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 模式选择 */}
      <div className="flex items-center space-x-4">
        <label className="flex items-center">
          <input
            type="radio"
            name="batchMode"
            checked={!isAutoMode}
            onChange={() => handleModeChange(false)}
            disabled={disabled}
            className="mr-2"
          />
          <span className="text-sm text-blue-700">手动输入</span>
        </label>
        <label className="flex items-center">
          <input
            type="radio"
            name="batchMode"
            checked={isAutoMode}
            onChange={() => handleModeChange(true)}
            disabled={disabled}
            className="mr-2"
          />
          <span className="text-sm text-blue-700">自动生成</span>
        </label>
      </div>

      {/* 手动输入模式 */}
      {!isAutoMode && (
        <div>
          <input
            type="text"
            value={value}
            onChange={(e) => handleManualChange(e.target.value)}
            placeholder="请输入批次号"
            disabled={disabled}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
          />
          <p className="mt-1 text-xs text-gray-500">
            支持字母、数字、连字符、下划线和中文字符，长度不超过50个字符
          </p>
        </div>
      )}

      {/* 自动生成模式 */}
      {isAutoMode && (
        <div className="space-y-3">
          {/* 批次类型选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              批次类型
            </label>
            <select
              value={selectedBatchType}
              onChange={(e) => setSelectedBatchType(e.target.value)}
              disabled={disabled || isGenerating}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
            >
              {batchTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label} ({type.value})
                </option>
              ))}
            </select>
          </div>

          {/* 生成的批次号显示 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              生成的批次号
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={value}
                readOnly
                disabled={disabled}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-blue-700 font-mono"
                placeholder={isGenerating ? "正在生成..." : "点击生成按钮"}
              />
              <button
                type="button"
                onClick={generateBatchNumber}
                disabled={disabled || isGenerating || !productId}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? '生成中...' : '生成'}
              </button>
            </div>
            {value && (
              <p className="mt-1 text-xs text-green-600">
                ✓ 批次号已生成：{selectedBatchType}-日期序号格式
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
