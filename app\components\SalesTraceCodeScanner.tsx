'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Medicine, BatchInfo, TraceCodeScanResult } from '@/types/medicine';

interface Props {
  onAddMedicine: (medicine: Medicine, quantity: number, traceCode?: string, batchInfo?: BatchInfo) => void;
  isOpen: boolean;
  onClose: () => void;
  orderId?: number;
}

type ScanStatus = 'idle' | 'scanning' | 'success' | 'error';

export default function SalesTraceCodeScanner({ onAddMedicine, isOpen, onClose, orderId }: Props) {
  const [scanStatus, setScanStatus] = useState<ScanStatus>('idle');
  const [statusMessage, setStatusMessage] = useState('');
  const [scannedCode, setScannedCode] = useState('');
  const [scanResult, setScanResult] = useState<TraceCodeScanResult | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // 重置状态
  const resetState = () => {
    setScanStatus('idle');
    setStatusMessage('');
    setScannedCode('');
    setScanResult(null);
  };

  // 当弹窗打开时，聚焦到输入框
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
      resetState();
    }
  }, [isOpen]);

  // 处理扫描输入
  const handleScanInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const code = e.target.value.trim();
    setScannedCode(code);
    
    // 当输入长度达到一定值时自动处理（模拟扫码枪）
    if (code.length >= 10) {
      processCode(code);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const code = scannedCode.trim();
      if (code) {
        processCode(code);
      }
    }
  };

  // 处理扫描的代码
  const processCode = async (code: string) => {
    try {
      setScanStatus('scanning');
      setStatusMessage(`正在识别代码: ${code}`);
      setScanResult(null);

      // 首先判断是否为药品追溯码
      const isDrugTraceCode = await checkIfDrugTraceCode(code);

      if (isDrugTraceCode) {
        // 处理药品追溯码
        await processTraceCode(code);
      } else {
        // 处理普通条形码
        await processBarcode(code);
      }

    } catch (error) {
      setScanStatus('error');
      setStatusMessage(error instanceof Error ? error.message : '扫描失败');
      console.error('扫描处理失败:', error);

      // 3秒后清除错误消息
      setTimeout(() => {
        setScanStatus('idle');
        setStatusMessage('');
      }, 3000);
    }
  };

  // 检查是否为药品追溯码
  const checkIfDrugTraceCode = async (code: string): Promise<boolean> => {
    // 药品追溯码格式判断：
    // 1. 长度通常大于等于15位
    // 2. 全数字格式
    // 3. 不以69开头（69开头通常是商品条码）
    if (code.length < 15 || code.startsWith('69') || !/^\d+$/.test(code)) {
      return false;
    }
    return true;
  };

  // 处理药品追溯码
  const processTraceCode = async (code: string) => {
    // 临时使用测试API进行开发测试
    const response = await fetch('/api/sales/test-trace-scan', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        traceCode: code,
        orderId: orderId
      }),
    });

    const data = await response.json();

    if (data.success) {
      setScanStatus('success');
      setStatusMessage(`追溯码扫描成功: ${data.data.localProduct.name}`);
      setScanResult(data.data);
    } else {
      throw new Error(data.message || '追溯码扫描失败');
    }
  };

  // 处理普通条形码或药品标识码
  const processBarcode = async (code: string) => {
    // 如果是7位数字，可能是药品标识码
    let searchCode = code;
    if (/^\d{7}$/.test(code)) {
      // 7位数字，直接作为药品标识码搜索
      searchCode = code;
    } else if (/^\d+$/.test(code) && code.length > 7) {
      // 长数字，可能是药品追溯码，提取前7位作为药品标识码
      searchCode = code.substring(0, 7);
    }

    const response = await fetch(`/api/products/barcode?code=${encodeURIComponent(searchCode)}`);
    const data = await response.json();

    if (data.success && data.data) {
      setScanStatus('success');
      setStatusMessage(`药品扫描成功: ${data.data.name}`);

      // 直接添加药品到订单
      onAddMedicine(data.data, 1);

      // 2秒后关闭弹窗
      setTimeout(() => {
        onClose();
      }, 2000);
    } else {
      throw new Error('未找到对应的药品');
    }
  };

  // 确认添加药品
  const handleConfirmAdd = () => {
    if (scanResult) {
      onAddMedicine(
        scanResult.localProduct,
        scanResult.quantity,
        scanResult.traceCode,
        scanResult.batchInfo
      );
      
      setScanStatus('idle');
      setStatusMessage('药品已添加到订单');
      
      // 1秒后关闭弹窗
      setTimeout(() => {
        onClose();
      }, 1000);
    }
  };

  // 继续扫描
  const handleContinueScan = () => {
    resetState();
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4"
      style={{
        zIndex: 10000,
        backgroundColor: 'rgba(0, 0, 0, 0.5)' // 确保50%透明度
      }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full mx-4 p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-blue-700">扫描药品代码</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="space-y-4">
          {/* 扫描输入框 */}
          <div>
            <label className="block text-sm font-medium text-black mb-1">
              扫描或输入代码
            </label>
            <input
              ref={inputRef}
              type="text"
              value={scannedCode}
              onChange={handleScanInput}
              onKeyDown={handleKeyDown}
              placeholder="请扫描条形码或药品追溯码"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-black placeholder-gray-600"
              disabled={scanStatus === 'scanning'}
            />
          </div>

          {/* 状态显示 */}
          {statusMessage && (
            <div className={`p-3 rounded-md ${
              scanStatus === 'success' ? 'bg-green-50 text-green-700' :
              scanStatus === 'error' ? 'bg-red-50 text-red-700' :
              'bg-blue-50 text-blue-700'
            }`}>
              <div className="flex items-center">
                {scanStatus === 'scanning' && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                )}
                <span className="text-sm">{statusMessage}</span>
              </div>
            </div>
          )}

          {/* 扫描结果显示 */}
          {scanResult && (
            <div className="bg-gray-50 p-4 rounded-md">
              <h4 className="font-medium text-blue-700 mb-2">扫描结果</h4>
              <div className="space-y-2 text-sm">
                <div><span className="font-medium">药品名称:</span> {scanResult.localProduct.name}</div>
                <div><span className="font-medium">规格:</span> {scanResult.localProduct.specification}</div>
                <div><span className="font-medium">生产厂家:</span> {scanResult.batchInfo.manufacturer}</div>
                <div><span className="font-medium">批次号:</span> {scanResult.batchInfo.batchNo}</div>
                <div><span className="font-medium">有效期:</span> {scanResult.batchInfo.expireDate}</div>
                <div><span className="font-medium">售价:</span> ¥{scanResult.localProduct.price.toFixed(2)}</div>
                <div><span className="font-medium">库存:</span> {scanResult.localProduct.stock_quantity}</div>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex space-x-3">
            {scanResult ? (
              <>
                <button
                  onClick={handleConfirmAdd}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                >
                  添加到订单
                </button>
                <button
                  onClick={handleContinueScan}
                  className="px-4 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
                >
                  继续扫描
                </button>
              </>
            ) : (
              <button
                onClick={onClose}
                className="w-full px-4 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors"
              >
                取消
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
