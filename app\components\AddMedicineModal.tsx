'use client';

import { useState, useEffect } from 'react';
import { Medicine } from '@/types/medicine';

interface AddMedicineModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (medicine: Medicine, quantity: number) => void;
  continuousMode?: boolean; // 是否为连续添加模式
}

export default function AddMedicineModal({ isOpen, onClose, onAdd, continuousMode = false }: AddMedicineModalProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMedicine, setSelectedMedicine] = useState<Medicine | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [medicines, setMedicines] = useState<Medicine[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // 从数据库获取药品数据
  useEffect(() => {
    const fetchMedicines = async () => {
      try {
        setLoading(true);
        // 从API获取药品数据
        const response = await fetch('/api/products');
        const data = await response.json();

        if (data.success) {
          // API 返回的是 data.data，而不是 data.data.products
          setMedicines(data.data || []);
        } else {
          setError(data.message || '获取药品数据失败');
        }
      } catch (err) {
        setError('获取药品数据失败');
        console.error('获取药品数据失败:', err);
      } finally {
        setLoading(false);
      }
    };

    if (isOpen) {
      fetchMedicines();
    }
  }, [isOpen]);

  // 根据搜索词过滤药品
  const filteredMedicines = medicines.filter(medicine =>
    medicine.name.includes(searchTerm) ||
    medicine.category_name.includes(searchTerm) ||
    medicine.specification.includes(searchTerm) ||
    (medicine.barcode && medicine.barcode.includes(searchTerm)) ||
    (medicine.trace_code && medicine.trace_code.includes(searchTerm))
  );

  const handleAdd = () => {
    if (selectedMedicine && quantity > 0) {
      onAdd(selectedMedicine, quantity);

      // 如果是连续添加模式，清空选择但不关闭弹窗
      if (continuousMode) {
        setSelectedMedicine(null);
        setQuantity(1);
        setSearchTerm('');
      } else {
        // 非连续模式，添加后关闭弹窗
        setSelectedMedicine(null);
        setQuantity(1);
        setSearchTerm('');
        onClose();
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
      <div className="bg-white rounded-lg shadow-xl w-11/12 md:w-3/4 lg:w-2/3 xl:w-1/2 max-h-[90vh] flex flex-col pointer-events-auto">
        <div className="flex justify-between items-center px-6 py-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">添加药品</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 focus:outline-none"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-6 flex-1 overflow-auto">
          <div className="mb-4">
            <div className="relative">
              <input
                type="text"
                placeholder="搜索药品名称、分类、规格、条形码或追溯码..."
                className="w-full px-4 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          <div className="overflow-auto max-h-[50vh]">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50 sticky top-0">
                <tr>
                  <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">选择</th>
                  <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">药品名称</th>
                  <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格</th>
                  <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
                  <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">条形码</th>
                  <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单价</th>
                  <th scope="col" className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">库存</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {loading ? (
                  <tr>
                    <td colSpan={7} className="px-3 py-4 text-center text-sm text-gray-500">
                      <div className="flex justify-center items-center">
                        <svg className="animate-spin h-5 w-5 mr-3 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        正在加载药品数据...
                      </div>
                    </td>
                  </tr>
                ) : error ? (
                  <tr>
                    <td colSpan={7} className="px-3 py-4 text-center text-sm text-red-500">
                      {error}
                    </td>
                  </tr>
                ) : filteredMedicines.length > 0 ? (
                  filteredMedicines.map((medicine) => (
                    <tr
                      key={medicine.id}
                      className={`hover:bg-blue-50 cursor-pointer ${selectedMedicine?.id === medicine.id ? 'bg-blue-50' : ''}`}
                      onClick={() => setSelectedMedicine(medicine)}
                    >
                      <td className="px-3 py-3 whitespace-nowrap">
                        <input
                          type="radio"
                          name="selectedMedicine"
                          checked={selectedMedicine?.id === medicine.id}
                          onChange={() => setSelectedMedicine(medicine)}
                          className="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300"
                        />
                      </td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm font-medium text-blue-700">{medicine.name}</td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.specification}</td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.category_name}</td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.barcode || '-'}</td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-blue-700 font-medium">¥{medicine.price.toFixed(2)}</td>
                      <td className="px-3 py-3 whitespace-nowrap text-sm text-gray-500">{medicine.stock_quantity}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="px-3 py-4 text-center text-sm text-gray-500">
                      未找到匹配的药品
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>

        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-4">
              {selectedMedicine && (
                <>
                  <span className="text-sm font-medium">已选：{selectedMedicine.name}</span>
                  <div className="flex items-center">
                    <span className="text-sm mr-2">数量：</span>
                    <div className="flex items-center border border-gray-300 rounded-md">
                      <button
                        className="px-2 py-1 text-gray-500 hover:text-gray-700 disabled:opacity-50"
                        disabled={quantity <= 1}
                        onClick={() => setQuantity(prev => Math.max(1, prev - 1))}
                      >
                        -
                      </button>
                      <input
                        type="number"
                        min="1"
                        max={selectedMedicine.stock_quantity}
                        value={quantity}
                        onChange={(e) => setQuantity(Math.min(parseInt(e.target.value) || 1, selectedMedicine.stock_quantity))}
                        className="w-16 text-center border-0 focus:ring-0 text-blue-700"
                      />
                      <button
                        className="px-2 py-1 text-gray-500 hover:text-gray-700 disabled:opacity-50"
                        disabled={quantity >= selectedMedicine.stock_quantity}
                        onClick={() => setQuantity(prev => Math.min(prev + 1, selectedMedicine.stock_quantity))}
                      >
                        +
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>
            <div className="flex gap-3">
              <button
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                {continuousMode ? '完成' : '取消'}
              </button>
              {continuousMode && (
                <button
                  onClick={handleAdd}
                  disabled={!selectedMedicine}
                  className={`px-4 py-2 rounded-md text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                    selectedMedicine ? 'bg-green-600 hover:bg-green-700' : 'bg-green-400 cursor-not-allowed'
                  }`}
                >
                  添加并继续
                </button>
              )}
              <button
                onClick={handleAdd}
                disabled={!selectedMedicine}
                className={`px-4 py-2 rounded-md text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                  selectedMedicine ? 'bg-blue-600 hover:bg-blue-700' : 'bg-blue-400 cursor-not-allowed'
                }`}
              >
                {continuousMode ? '添加并关闭' : '添加到订单'}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}