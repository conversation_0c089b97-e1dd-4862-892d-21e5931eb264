/**
 * 药品相关的TypeScript类型定义
 */

// 药品基础信息接口 - 用于销售订单等场景
export interface Medicine {
  id: number;
  name: string;
  generic_name?: string;
  specification: string;
  manufacturer: string;
  price: number;
  category_name: string;
  drug_identification_code?: string; // 药品标识码（药品追溯码前7位）
  trace_code?: string;
  approval_number?: string;
}

// 药品完整信息接口 - 用于药品管理等场景
export interface MedicineDetail extends Medicine {
  description?: string;
  category_id: number;
  supplier_id?: number;
  supplier_name?: string;
  dosage_form: string;
  cost_price: number;
  is_prescription: boolean;
  is_medical_insurance: boolean;
  storage_condition: string;
  status: 'active' | 'inactive';
  created_at?: string;
  updated_at?: string;
}

// 药品表单数据接口
export interface MedicineFormData {
  name: string;
  generic_name: string;
  description: string;
  drug_identification_code: string; // 药品标识码（药品追溯码前7位）
  category_id: number;
  supplier_id?: number;
  manufacturer: string;
  approval_number: string;
  specification: string;
  dosage_form: string;
  price: number;
  cost_price: number;
  is_prescription: boolean;
  is_medical_insurance: boolean;
  storage_condition: string;
  status: 'active' | 'inactive';
}

// 批次信息接口
export interface BatchInfo {
  batchNo: string;
  expireDate: string;
  productionDate?: string;
  manufacturer: string;
  drugName: string;
  specification: string;
  approvalNumber?: string;
  drug_identification_code?: string; // 药品标识码（药品追溯码前7位）
  localBatchId?: number;
  localBatchQuantity?: number;
}

// 追溯码扫描结果接口
export interface TraceCodeScanResult {
  traceCode: string;
  localProduct: Medicine;
  batchInfo: BatchInfo;
  canSell: boolean;
  quantity: number;
}

// 药品分类接口
export interface MedicineCategory {
  id: number;
  name: string;
  description?: string;
  parent_id?: number;
  status: 'active' | 'inactive';
  created_at?: string;
  updated_at?: string;
}

// API响应接口
export interface MedicineApiResponse {
  success: boolean;
  message?: string;
  data?: Medicine | Medicine[] | MedicineDetail | MedicineDetail[];
  error?: string;
}

// 药品列表响应接口
export interface MedicineListResponse {
  success: boolean;
  data: Medicine[] | MedicineDetail[];
  total?: number;
  page?: number;
  pageSize?: number;
  message?: string;
  error?: string;
}

// 药品统计信息接口
export interface MedicineStats {
  total: number;
  active: number;
  inactive: number;
  prescription: number;
  non_prescription: number;
}

// 储存条件选项
export const STORAGE_CONDITIONS = [
  { value: '常温', label: '常温' },
  { value: '阴凉', label: '阴凉' },
  { value: '冷藏', label: '冷藏' },
  { value: '冷冻', label: '冷冻' }
] as const;

// 剂型选项
export const DOSAGE_FORMS = [
  { value: '片剂', label: '片剂' },
  { value: '胶囊', label: '胶囊' },
  { value: '颗粒', label: '颗粒' },
  { value: '口服液', label: '口服液' },
  { value: '注射液', label: '注射液' },
  { value: '软膏', label: '软膏' },
  { value: '滴眼液', label: '滴眼液' },
  { value: '喷雾剂', label: '喷雾剂' },
  { value: '其他', label: '其他' }
] as const;

// 药品状态选项
export const MEDICINE_STATUS_OPTIONS = [
  { value: 'active', label: '正常', color: 'green' },
  { value: 'inactive', label: '停用', color: 'red' }
] as const;
