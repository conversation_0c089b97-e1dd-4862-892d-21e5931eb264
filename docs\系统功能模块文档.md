# 药店零售管理系统功能模块文档

## 系统概述

药店零售管理系统是一个基于Next.js开发的现代化药店管理平台，采用中文数据库设计，集成码上放心平台药品追溯功能，为药店提供全面的业务管理解决方案。

### 技术架构
- **前端框架**: Next.js 15.3.1 + React 19.0 + TypeScript
- **样式框架**: Tailwind CSS 4
- **数据库**: SQLite3 (中文表名和字段名)
- **UI组件**: Headless UI
- **第三方集成**: 码上放心开放平台

## 功能模块总览

### 核心业务模块
1. [销售管理](#1-销售管理模块) - 销售订单创建、管理、结算
2. [药品管理](#2-药品管理模块) - 药品信息管理、分类管理
3. [供应商管理](#3-供应商管理模块) - 供应商信息管理、GSP认证
4. [库存管理](#4-库存管理模块) - 入库、出库、盘点、批次管理
5. [订单管理](#5-订单管理模块) - 统一订单查询、筛选、统计

### 系统管理模块
6. [系统设置](#6-系统设置模块) - 基本设置、平台配置
7. [数据管理](#7-数据管理模块) - 数据库管理、备份恢复

### 共享组件模块
8. [通用组件](#8-通用组件模块) - 弹窗、扫码、表单组件

---

## 1. 销售管理模块

### 功能概述
销售管理模块是系统的核心业务模块，提供完整的销售流程管理，从订单创建到结算完成的全流程支持。

### 主要功能

#### 1.1 销售订单管理
- **订单创建**: 支持手动添加和扫码添加药品
- **订单编辑**: 修改订单信息、调整商品数量
- **订单状态**: 草稿、待处理、已完成、已取消
- **订单查询**: 按编号、客户、时间等条件查询

#### 1.2 客户管理
- **客户信息**: 姓名、电话、会员等级
- **购买历史**: 客户历史订单记录
- **会员管理**: 会员积分、优惠管理

#### 1.3 结算功能
- **金额计算**: 总金额、折扣、应付金额自动计算
- **支付方式**: 现金、刷卡、移动支付等
- **找零计算**: 实时计算找零金额
- **小票打印**: 支持多种规格小票打印

#### 1.4 销售统计
- **日销售统计**: 当日销售额、订单数量
- **热销商品**: 销售排行榜
- **销售趋势**: 销售数据趋势分析

### 技术实现
- **页面路径**: `/sales`、`/sales/new`、`/sales/[id]`
- **API接口**: `/api/orders`、`/api/orders/[id]`
- **数据表**: 销售订单、订单明细、客户信息

---

## 2. 药品管理模块

### 功能概述
药品管理模块负责药品基础信息的维护，支持药品分类管理、扫码识别、追溯码管理等功能。

### 主要功能

#### 2.1 药品信息管理
- **基本信息**: 药品名称、通用名、规格、剂型
- **分类管理**: 药品分类、处方药标识、医保标识
- **价格管理**: 进价、售价、利润率计算
- **库存信息**: 当前库存、最低库存预警

#### 2.2 药品分类管理
- **分类体系**: 多级分类结构
- **分类维护**: 添加、编辑、删除分类
- **分类统计**: 各分类药品数量统计

#### 2.3 扫码识别功能
- **条码扫描**: 支持扫码枪输入
- **追溯码识别**: 区分药品追溯码和商品条码
- **自动填充**: 扫码后自动填充药品信息
- **码上放心集成**: 通过追溯码获取药品详细信息

#### 2.4 储存管理
- **储存条件**: 常温、阴凉、冷藏等条件设置
- **有效期管理**: 生产日期、有效期跟踪
- **批次管理**: 药品批次信息管理

### 技术实现
- **页面路径**: `/products`
- **API接口**: `/api/products`、`/api/categories`、`/api/drug-trace`
- **数据表**: 药品信息、药品分类
- **第三方集成**: 码上放心平台API

---

## 3. 供应商管理模块

### 功能概述
供应商管理模块提供完整的供应商信息管理，包括基本信息、资质管理、合作状态跟踪等功能。

### 主要功能

#### 3.1 供应商信息管理
- **基本信息**: 名称、联系人、电话、地址
- **财务信息**: 税号、银行账户、开户行
- **合作状态**: 活跃、暂停、终止等状态

#### 3.2 资质管理
- **营业执照**: 执照号、有效期管理
- **GSP认证**: GSP证书号、有效期跟踪
- **经营范围**: 供应商经营范围记录
- **质量负责人**: 质量负责人信息

#### 3.3 供应商评估
- **合作评级**: 供应商合作评级
- **质量记录**: 供货质量记录
- **服务评价**: 服务质量评价

### 技术实现
- **页面路径**: `/suppliers`
- **API接口**: `/api/suppliers`、`/api/suppliers/[id]`
- **数据表**: 供应商信息表
- **关联功能**: 与药品管理模块关联

---

## 4. 库存管理模块

### 功能概述
库存管理模块提供全面的库存操作功能，包括入库、出库、盘点、批次管理等，确保库存数据的准确性。

### 主要功能

#### 4.1 入库管理
- **采购入库**: 供应商采购入库
- **退货入库**: 客户退货入库
- **调拨入库**: 门店间调拨入库
- **批次信息**: 批次号、生产日期、有效期

#### 4.2 出库管理
- **销售出库**: 销售订单出库
- **调拨出库**: 门店间调拨出库
- **报损出库**: 过期、损坏药品出库
- **出库审核**: 出库单据审核流程

#### 4.3 库存盘点
- **全盘盘点**: 全部药品盘点
- **抽盘盘点**: 部分药品抽查盘点
- **差异处理**: 盘点差异分析和处理
- **盘点报告**: 盘点结果报告生成

#### 4.4 批次管理
- **批次跟踪**: 药品批次全程跟踪
- **有效期预警**: 临期药品预警
- **批次查询**: 按批次查询库存
- **先进先出**: FIFO库存管理

### 技术实现
- **页面路径**: `/inventory`
- **API接口**: `/api/inventory`
- **数据表**: 库存记录、批次信息
- **组件**: StockInForm、StockOutForm、BatchManagement、InventoryCount

---

## 5. 订单管理模块

### 功能概述
订单管理模块提供统一的订单查询和管理功能，整合销售订单、入库订单、出库订单等多种订单类型。

### 主要功能

#### 5.1 统一订单查询
- **多类型整合**: 销售、入库、出库、盘点、调整订单
- **统一界面**: 一站式订单管理界面
- **分页查询**: 高效的分页查询机制
- **排序功能**: 多字段排序支持

#### 5.2 高级筛选功能
- **订单类型筛选**: 按订单类型筛选
- **状态筛选**: 按订单状态筛选
- **时间筛选**: 日期范围筛选
- **快捷筛选**: 今日、本周、本月等快捷选项

#### 5.3 全文搜索功能
- **订单编号搜索**: 精确订单编号搜索
- **药品名称搜索**: 模糊药品名称搜索
- **条码搜索**: 药品条码搜索
- **批次号搜索**: 批次号搜索

#### 5.4 统计分析功能
- **订单统计**: 订单数量、金额统计
- **趋势分析**: 订单趋势分析
- **状态分布**: 订单状态分布统计
- **可视化图表**: 数据可视化展示

### 技术实现
- **页面路径**: `/orders`、`/orders/[id]`
- **API接口**: `/api/orders/unified-simple`、`/api/orders/search-simple`
- **数据表**: 销售订单、库存记录
- **权限控制**: 基于角色的访问控制

---

## 6. 系统设置模块

### 功能概述
系统设置模块提供系统基本配置、第三方平台集成配置、业务参数设置等功能。

### 主要功能

#### 6.1 基本设置
- **药店信息**: 药店名称、地址、联系方式
- **订单编号**: 订单编号生成规则设置
- **小票设置**: 小票尺寸、打印格式设置
- **业务参数**: 各种业务参数配置

#### 6.2 码上放心平台配置
- **API凭据**: AppKey、AppSecret配置
- **企业信息**: 企业ID、API地址配置
- **功能开关**: 平台功能启用/禁用
- **参数验证**: 配置参数有效性验证

#### 6.3 系统管理
- **默认值恢复**: 系统设置恢复默认值
- **配置备份**: 系统配置备份和恢复
- **参数导入导出**: 配置参数批量管理

### 技术实现
- **页面路径**: `/settings`
- **API接口**: `/api/settings`
- **数据表**: settings表
- **本地存储**: localStorage备份机制

---

## 7. 数据管理模块

### 功能概述
数据管理模块提供数据库管理、数据备份恢复、数据迁移等功能，确保数据安全和系统稳定。

### 主要功能

#### 7.1 数据库管理
- **数据库初始化**: 自动创建数据库表结构
- **数据库备份**: 定期数据库备份
- **数据库恢复**: 从备份文件恢复数据
- **数据库优化**: 数据库性能优化

#### 7.2 数据迁移
- **结构迁移**: 数据库结构升级迁移
- **数据迁移**: 历史数据迁移
- **版本管理**: 数据库版本管理
- **回滚机制**: 迁移失败回滚

#### 7.3 数据监控
- **连接监控**: 数据库连接状态监控
- **性能监控**: 查询性能监控
- **空间监控**: 数据库空间使用监控
- **错误日志**: 数据库错误日志记录

### 技术实现
- **组件**: DatabaseManagement
- **API接口**: `/api/db-management`、`/api/db-migrate`
- **数据库**: SQLite3
- **备份目录**: `/db/backups/`

---

## 8. 通用组件模块

### 功能概述
通用组件模块提供系统中复用的UI组件，包括各种弹窗组件、扫码组件、表单组件等。

### 主要组件

#### 8.1 弹窗组件
- **ConfirmDeleteDialog**: 删除确认对话框
- **ErrorModal**: 错误提示弹窗
- **SuccessModal**: 成功提示弹窗
- **AlertDialog**: 通用警告对话框

#### 8.2 扫码组件
- **BarcodeScanner**: 药品管理扫码组件
- **SalesBarcodeScanner**: 销售管理扫码组件
- **DrugTraceInfo**: 药品追溯信息显示组件

#### 8.3 表单组件
- **MedicineForm**: 药品信息表单
- **SupplierForm**: 供应商信息表单
- **AddMedicineModal**: 添加药品模态框

### 设计规范
- **统一主题**: 蓝色主题 (text-blue-700)
- **响应式设计**: 支持多设备适配
- **交互一致性**: 统一的交互行为
- **可访问性**: 支持键盘导航和屏幕阅读器

### 技术实现
- **组件目录**: `/app/components/`
- **样式规范**: 遵循UI设计规范
- **状态管理**: React Hooks
- **类型定义**: TypeScript接口

## 系统特色功能

### 1. 中文数据库设计
- 采用中文表名和字段名
- 符合中国用户使用习惯
- 提高系统可维护性

### 2. 码上放心平台集成
- 药品追溯码识别
- 自动获取药品信息
- 符合药品监管要求

### 3. 响应式设计
- 支持桌面、平板、手机
- 统一的用户体验
- 现代化的界面设计

### 4. 模块化架构
- 功能模块独立
- 组件高度复用
- 易于扩展和维护

## 数据库设计

### 核心业务表
- **药品信息** - 存储药品基本信息、价格、库存等
- **药品分类** - 药品分类管理，支持多级分类
- **销售订单** - 销售订单主表信息
- **订单明细** - 销售订单商品明细
- **客户信息** - 客户基本信息和会员信息
- **供应商信息表** - 供应商详细信息和资质管理
- **库存记录** - 库存变动记录，包含入库、出库等操作

### 系统管理表
- **users** - 系统用户管理
- **settings** - 系统配置信息
- **audit_logs** - 操作审计日志

### 数据库特点
- 使用中文表名和字段名
- SQLite3轻量级数据库
- 支持事务处理
- 完整的索引优化

## API接口规范

### RESTful API设计
- **GET** - 查询数据
- **POST** - 创建数据
- **PUT** - 更新数据
- **DELETE** - 删除数据

### 统一响应格式
```json
{
  "success": boolean,
  "data": any,
  "message": string,
  "error": string
}
```

### 主要API端点
- `/api/products` - 药品管理API
- `/api/suppliers` - 供应商管理API
- `/api/orders` - 订单管理API
- `/api/inventory` - 库存管理API
- `/api/settings` - 系统设置API
- `/api/drug-trace` - 药品追溯API

## 权限管理

### 角色定义
- **管理员** - 系统全部功能权限
- **操作员** - 基本业务操作权限
- **查看员** - 只读查询权限

### 权限控制
- 基于角色的访问控制(RBAC)
- API级别权限验证
- 页面级别权限控制
- 操作审计日志记录

## 系统集成

### 码上放心平台集成
- **API接口**: 阿里健康药品追溯平台
- **功能**: 药品追溯码查询、上传出入库单据
- **认证**: AppKey/AppSecret认证机制
- **数据格式**: 标准JSON格式交互

### 扫码设备支持
- 支持USB扫码枪
- 支持手机摄像头扫码
- 自动识别条码类型
- 实时扫码反馈

## 部署和运维

### 部署方式
- **本地部署** - 单机部署方案
- **云端部署** - Vercel等云平台部署
- **Docker部署** - 容器化部署方案

### 系统要求
- **Node.js** - 18.0以上版本
- **数据库** - SQLite3
- **浏览器** - 现代浏览器支持
- **设备** - 支持扫码枪等外设

### 备份策略
- 自动数据库备份
- 配置文件备份
- 定期备份验证
- 快速恢复机制

## 更新记录

- **2025-07-05**: 初始版本创建
- 完成了系统所有功能模块的详细文档
- 建立了完整的功能模块体系说明
- 提供了技术实现和特色功能介绍
- 添加了数据库设计、API规范、权限管理等详细信息
