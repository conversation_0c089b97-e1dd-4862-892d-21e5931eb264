import { NextRequest, NextResponse } from 'next/server';
import { run, query } from '@/lib/db';

interface RouteParams {
  params: {
    batchId: string;
  };
}

export async function POST(_req: NextRequest, { params }: RouteParams) {
  try {
    const { batchId } = params;

    if (!batchId) {
      return NextResponse.json({
        success: false,
        message: '批次ID不能为空'
      }, { status: 400 });
    }
    
    try {
      // 开始事务
      await run('BEGIN TRANSACTION');
      
      // 1. 查找批次
      const batch = await query(`
        SELECT * FROM 药品批次表 WHERE 编号 = ?
      `, [batchId]);

      if (!batch || batch.length === 0) {
        return NextResponse.json({
          success: false,
          message: '未找到指定批次'
        }, { status: 404 });
      }

      const currentBatch = batch[0];

      // 2. 更新批次状态为过期
      await run(`
        UPDATE 药品批次表
        SET 状态 = 'expired', 更新时间 = CURRENT_TIMESTAMP
        WHERE 编号 = ?
      `, [batchId]);

      // 3. 如果批次有剩余数量，需要减少产品库存
      if (currentBatch.剩余数量 > 0 && currentBatch.状态 === 'active') {
        await run(`
          UPDATE 药品信息
          SET 库存数量 = 库存数量 - ?
          WHERE 编号 = ?
        `, [currentBatch.剩余数量, currentBatch.药品编号]);

        // 4. 创建库存记录
        await run(`
          INSERT INTO 库存记录
          (药品编号, 操作类型, 数量变化, 操作前库存, 操作后库存, 批次号, 有效期, 操作人, 备注)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          currentBatch.药品编号,
          '出库',
          -currentBatch.剩余数量, // 负数表示出库
          0, // 操作前库存，这里简化处理
          0, // 操作后库存，这里简化处理
          currentBatch.批次号,
          currentBatch.有效期,
          'system',
          `批次 ${currentBatch.批次号} 已标记为过期/作废`
        ]);
      }
      
      // 提交事务
      await run('COMMIT');
      
      return NextResponse.json({ 
        success: true, 
        data: {
          message: '批次已成功标记为过期/作废'
        }
      });
    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      throw error;
    }
  } catch (error) {
    console.error('标记批次为过期时出错:', error);
    return NextResponse.json({ 
      success: false, 
      message: '服务器处理批次过期操作时出错' 
    }, { status: 500 });
  }
} 