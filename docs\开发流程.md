## 🧩 模块开发流程概述（通用流程）

每个模块的开发都可以遵循以下流程：

### 1. **需求明确**

* 梳理功能点（来自你提供的文档）
* 明确字段、交互方式、业务规则
* 绘制简单的 UI 草图或流程图

### 2. **数据库建表**

* 在 SQLite3 中设计表结构（中文字段/表名）
* 建立必要的索引、外键、默认值
* 使用 migration 脚本或 Prisma 管理结构

### 3. **后端接口**

* 目录：`/pages/api/[module]/...`
* 按 RESTful 风格编写 GET / POST / PUT / DELETE 接口
* 实现权限判断、中间件校验、错误处理
* 所有接口应返回统一格式 `{ success, data, message, error }`

### 4. **前端页面**

* 页面路径如 `/sales/[id]`
* 使用 React + TypeScript 编写组件
* 使用 Tailwind CSS 布局
* 表单处理、扫码、模态框调用通用组件
* 状态管理用 Hook / 上下文（如 useOrderContext）

### 5. **联调测试**

* 使用 Postman 或浏览器测试 API
* 页面联调接口
* 处理异常、空数据、权限受限等情况

### 6. **上线/备份**

* 确保数据库自动备份
* 前端打包构建、Node 启动
* 若用 Docker / Vercel，确保环境变量正确

---

## 🔧 各模块子模块及开发顺序建议

### 1. 销售管理模块

#### 子模块流程：

* 客户信息录入 ➜ 订单创建（扫码/手动） ➜ 支付结算 ➜ 打印小票 ➜ 订单查询统计

#### 重点开发项：

* 条码扫码添加药品（组件复用）
* 自动计算金额、找零
* 销售统计图表（可用 Chart.js）
* 订单状态管理

---

### 2. 药品管理模块

#### 子模块流程：

* 药品信息录入 ➜ 分类设置 ➜ 条码/追溯码管理 ➜ 价格调整 ➜ 库存预警设置

#### 重点开发项：

* 多级分类管理（树结构）
* 条码扫码录入 ➜ 自动填充信息
* 集成“码上放心”平台（调用 `/api/drug-trace`）
* 储存条件管理 + 批次信息

---

### 3. 供应商管理模块

#### 子模块流程：

* 供应商录入 ➜ 资质上传 ➜ 合作评级 ➜ 服务记录

#### 重点开发项：

* GSP证书到期提醒（定时任务/前端提醒）
* 合作状态颜色标签显示
* 附件上传（营业执照、资质）

---

### 4. 库存管理模块

#### 子模块流程：

* 入库（采购/退货） ➜ 出库（销售/调拨/报损） ➜ 批次管理 ➜ 库存盘点 ➜ 差异处理

#### 重点开发项：

* 入库/出库表单统一组件 `StockForm`
* FIFO先进先出策略
* 临期药品预警（颜色/时间判断）
* 盘点差异自动比较

---

### 5. 订单管理模块

#### 子模块流程：

* 统一订单列表 ➜ 多条件筛选 ➜ 点击查看详情 ➜ 统计图表展示

#### 重点开发项：

* 全文搜索（模糊查询药品名/批次）
* 快捷筛选按钮（如“今日”、“本周”）
* 数据图表（趋势/分布）

---

### 6. 系统设置模块

#### 子模块流程：

* 药店基本资料 ➜ 打印设置 ➜ 第三方平台配置 ➜ 默认值/导入导出

#### 重点开发项：

* 小票格式设置（文本 + 模拟预览）
* API参数校验（格式是否正确）
* 设置页面保存成功提示

---

### 7. 数据管理模块

#### 子模块流程：

* 数据备份 ➜ 恢复 ➜ 迁移（版本升级）➜ 日志监控

#### 重点开发项：

* 数据备份按钮 ➜ 自动打包数据库
* 手动恢复备份文件
* 数据迁移版本管理（建议用 JSON + 标记版本号）

---

### 8. 通用组件模块

建议目录结构：

```
/app/components/
    Modal/
        ConfirmDeleteDialog.tsx
        ErrorModal.tsx
    Barcode/
        BarcodeScanner.tsx
        DrugTraceInfo.tsx
    Forms/
        MedicineForm.tsx
        SupplierForm.tsx
```

通用组件应该：

* 保持 UI 样式统一（Tailwind 的主题色、圆角、阴影等）
* 使用 `React Hook Form` + `Zod` 做表单校验
* 支持弹窗内部控制和外部触发（如 `ref`、`onOpen`）

---

## ✅ 建议开发顺序（渐进上线）

1. **药品管理模块**（最核心数据）
2. **销售管理模块**（带客户管理）
3. **库存管理模块**（出入库联动）
4. **订单管理模块**（统一查询）
5. **供应商管理模块**（采购支持）
6. **系统设置模块**
7. **数据管理模块**
8. **通用组件整理与优化**