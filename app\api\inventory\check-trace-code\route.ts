import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const traceCode = searchParams.get('code');

    if (!traceCode) {
      return NextResponse.json({
        success: false,
        message: '请提供追溯码'
      }, { status: 400 });
    }

    // 检查追溯码是否已经在库存记录中使用过
    const existingRecords = await query(
      `SELECT
        编号,
        药品编号,
        操作类型,
        操作时间,
        追溯码
      FROM 库存记录
      WHERE 追溯码 LIKE ?`,
      [`%${traceCode}%`]
    );

    const isUsed = existingRecords.length > 0;

    return NextResponse.json({
      success: true,
      data: {
        isUsed,
        records: existingRecords,
        message: isUsed ? '该追溯码已被使用' : '追溯码可以使用'
      }
    });

  } catch (error) {
    console.error('检查追溯码失败:', error);
    return NextResponse.json({
      success: false,
      message: '检查追溯码时发生错误'
    }, { status: 500 });
  }
}
