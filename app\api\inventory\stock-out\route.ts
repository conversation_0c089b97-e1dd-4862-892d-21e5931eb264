import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

interface StockOutRequest {
  product_id: number;
  quantity: number;
  reason: string;
  stock_out_date: string;
  notes?: string;
  trace_codes?: string[];
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json() as StockOutRequest;
    const { product_id, quantity, reason, stock_out_date, notes, trace_codes } = body;

    // 验证数据
    if (!product_id || !quantity || !reason || !stock_out_date) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数：product_id, quantity, reason, stock_out_date'
      }, { status: 400 });
    }

    if (quantity <= 0) {
      return NextResponse.json({
        success: false,
        message: '出库数量必须大于0'
      }, { status: 400 });
    }

    // 验证日期格式
    if (isNaN(Date.parse(stock_out_date))) {
      return NextResponse.json({
        success: false,
        message: '出库日期格式不正确'
      }, { status: 400 });
    }

    // 1. 获取产品信息
    const productResult = await query(
      'SELECT 编号, 名称, 库存数量, 规格, 生产厂家, 批准文号 FROM 药品信息 WHERE 编号 = ?',
      [product_id]
    );

    if (productResult.length === 0) {
      return NextResponse.json({
        success: false,
        message: '未找到指定的药品'
      }, { status: 404 });
    }

    const product = productResult[0];
    const currentStock = product.库存数量 || 0;

    // 2. 检查库存是否足够
    if (currentStock < quantity) {
      return NextResponse.json({
        success: false,
        message: `库存不足，当前库存：${currentStock}，需要出库：${quantity}`
      }, { status: 400 });
    }

    const newStock = currentStock - quantity;

    // 开始事务
    await run('BEGIN TRANSACTION');

    try {
      // 3. 创建库存记录（出库）
      const stockOutResult = await run(
        `INSERT INTO 库存记录 (
          药品编号, 操作类型, 数量变化, 操作前库存, 操作后库存,
          操作人, 备注, 操作时间
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          product_id,
          '出库',
          -quantity, // 出库为负数
          currentStock,
          newStock,
          '系统操作员', // 实际应用中应该从认证信息获取
          `${reason}${notes ? ' - ' + notes : ''}`,
          stock_out_date
        ]
      );

      const inventoryId = stockOutResult.lastID;

      // 4. 更新药品库存
      await run(
        'UPDATE 药品信息 SET 库存数量 = ? WHERE 编号 = ?',
        [newStock, product_id]
      );

      // 5. 处理批次出库（FIFO - 先进先出）
      try {
        let remainingQuantity = quantity;

        // 获取该药品的所有可用批次，按有效期排序（先进先出）
        const availableBatches = await query(
          `SELECT 编号, 批次号, 剩余数量, 有效期
           FROM batch
           WHERE 药品编号 = ? AND 状态 = 'active' AND 剩余数量 > 0
           ORDER BY 有效期 ASC, 创建时间 ASC`,
          [product_id]
        );

        for (const batch of availableBatches) {
          if (remainingQuantity <= 0) break;

          const batchQuantityToUse = Math.min(remainingQuantity, batch.剩余数量);

          // 更新批次剩余数量
          await run(
            'UPDATE batch SET 剩余数量 = 剩余数量 - ?, 更新时间 = CURRENT_TIMESTAMP WHERE 编号 = ?',
            [batchQuantityToUse, batch.编号]
          );

          // 如果批次用完，标记为耗尽
          if (batch.剩余数量 - batchQuantityToUse <= 0) {
            await run(
              'UPDATE batch SET 状态 = \'depleted\', 更新时间 = CURRENT_TIMESTAMP WHERE 编号 = ?',
              [batch.编号]
            );
          }

          remainingQuantity -= batchQuantityToUse;
        }

        if (remainingQuantity > 0) {
          console.warn(`出库完成，但有 ${remainingQuantity} 数量无法从批次中扣除`);
        }
      } catch (batchError) {
        console.error('处理批次出库失败:', batchError);
        // 不影响主流程，继续执行
      }

      // 6. 如果有追溯码，保存追溯码记录
      if (trace_codes && trace_codes.length > 0) {
        for (const traceCode of trace_codes) {
          if (traceCode.trim()) {
            await run(
              `INSERT INTO 药品追溯码记录 (
                库存记录编号, 药品编号, 追溯码, 操作类型
              ) VALUES (?, ?, ?, ?)`,
              [inventoryId, product_id, traceCode.trim(), '出库']
            );
          }
        }
      }

      // 提交事务
      await run('COMMIT');

      return NextResponse.json({
        success: true,
        message: '出库成功',
        data: {
          inventory_id: inventoryId,
          product_id: product_id,
          product_name: product.名称,
          quantity: quantity,
          reason: reason,
          previous_stock: currentStock,
          new_stock: newStock,
          trace_codes_count: trace_codes ? trace_codes.length : 0
        }
      });

    } catch (transactionError) {
      // 回滚事务
      await run('ROLLBACK');
      console.error('出库事务处理错误:', transactionError);
      throw transactionError;
    }

  } catch (error) {
    console.error('出库操作失败:', error);
    return NextResponse.json({
      success: false,
      message: '出库操作失败: ' + (error instanceof Error ? error.message : '未知错误')
    }, { status: 500 });
  }
}