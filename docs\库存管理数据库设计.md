# 库存管理数据库设计

## 概述

由于药品管理模块已将库存功能分离，现需要重新设计独立的库存管理数据库表结构，支持药品入库、出库、批次管理、库存盘点、过期预警等功能。

## 核心表结构设计

### 1. 药品库存主表 (药品库存)

存储每个药品的当前库存汇总信息，支持快速库存查询。

```sql
CREATE TABLE 药品库存 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  药品编号 INTEGER NOT NULL UNIQUE,
  当前库存 INTEGER NOT NULL DEFAULT 0,
  最低库存 INTEGER DEFAULT 0,
  最高库存 INTEGER DEFAULT 0,
  平均成本价 DECIMAL(10,2) DEFAULT 0,
  最后入库时间 DATETIME,
  最后出库时间 DATETIME,
  创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (药品编号) REFERENCES 药品信息(编号)
);
```

**字段说明：**
- `当前库存`: 实时库存数量，通过批次表汇总计算
- `最低库存`: 库存预警阈值
- `最高库存`: 库存上限
- `平均成本价`: 加权平均成本价
- `最后入库时间/最后出库时间`: 用于库存分析

### 2. 药品批次表 (药品批次表) - 优化现有结构

```sql
CREATE TABLE 药品批次表 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  药品编号 INTEGER NOT NULL,
  批次号 TEXT NOT NULL,
  生产日期 DATE,
  有效期 DATE NOT NULL,
  入库数量 INTEGER NOT NULL DEFAULT 0,
  当前数量 INTEGER NOT NULL DEFAULT 0,
  供应商编号 INTEGER,
  入库成本价 DECIMAL(10,2),
  状态 TEXT DEFAULT 'active' CHECK (状态 IN ('active', 'expired', 'depleted', 'locked')),
  创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  备注 TEXT,
  FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
  FOREIGN KEY (供应商编号) REFERENCES 供应商(编号),
  UNIQUE(药品编号, 批次号)
);
```

**状态说明：**
- `active`: 正常可用
- `expired`: 已过期
- `depleted`: 已用完
- `locked`: 锁定状态（盘点中）

### 3. 库存变动记录表 (库存变动记录) - 重构现有表

```sql
CREATE TABLE 库存变动记录 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  药品编号 INTEGER NOT NULL,
  批次编号 INTEGER,
  操作类型 TEXT NOT NULL CHECK (操作类型 IN ('入库', '出库', '盘点', '调整', '过期处理')),
  变动数量 INTEGER NOT NULL,
  变动前数量 INTEGER NOT NULL DEFAULT 0,
  变动后数量 INTEGER NOT NULL DEFAULT 0,
  单价 DECIMAL(10,2),
  总金额 DECIMAL(10,2),
  供应商编号 INTEGER,
  关联单据类型 TEXT CHECK (关联单据类型 IN ('采购单', '销售单', '盘点单', '调整单', '报损单')),
  关联单据编号 TEXT,
  操作人 TEXT,
  操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  备注 TEXT,
  码上放心单据号 TEXT,
  码上放心上传状态 TEXT CHECK (码上放心上传状态 IN ('pending', 'success', 'failed')),
  码上放心上传时间 DATETIME,
  码上放心响应 TEXT,
  FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
  FOREIGN KEY (批次编号) REFERENCES 药品批次表(编号),
  FOREIGN KEY (供应商编号) REFERENCES 供应商(编号)
);
```

### 4. 库存盘点表 (库存盘点)

```sql
CREATE TABLE 库存盘点 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  盘点编号 TEXT UNIQUE NOT NULL,
  盘点名称 TEXT NOT NULL,
  盘点类型 TEXT CHECK (盘点类型 IN ('全盘', '抽盘', '动态盘点')) DEFAULT '全盘',
  盘点状态 TEXT CHECK (盘点状态 IN ('进行中', '已完成', '已取消')) DEFAULT '进行中',
  开始时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  完成时间 DATETIME,
  盘点人 TEXT,
  审核人 TEXT,
  备注 TEXT,
  创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 5. 库存盘点明细表 (库存盘点明细)

```sql
CREATE TABLE 库存盘点明细 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  盘点编号 INTEGER NOT NULL,
  药品编号 INTEGER NOT NULL,
  批次编号 INTEGER,
  账面数量 INTEGER NOT NULL DEFAULT 0,
  实盘数量 INTEGER,
  差异数量 INTEGER DEFAULT 0,
  差异原因 TEXT,
  盘点状态 TEXT CHECK (盘点状态 IN ('待盘点', '已盘点', '有差异', '已处理')) DEFAULT '待盘点',
  盘点时间 DATETIME,
  备注 TEXT,
  FOREIGN KEY (盘点编号) REFERENCES 库存盘点(编号),
  FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
  FOREIGN KEY (批次编号) REFERENCES 药品批次表(编号)
);
```

### 6. 过期预警配置表 (过期预警配置)

```sql
CREATE TABLE 过期预警配置 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  药品编号 INTEGER,
  药品分类编号 INTEGER,
  预警天数 INTEGER NOT NULL DEFAULT 30,
  预警级别 TEXT CHECK (预警级别 IN ('低', '中', '高')) DEFAULT '中',
  是否启用 BOOLEAN DEFAULT 1,
  创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
  FOREIGN KEY (药品分类编号) REFERENCES 药品分类(编号)
);
```

## 视图设计

### 1. 库存汇总视图 (库存汇总视图)

```sql
CREATE VIEW 库存汇总视图 AS
SELECT 
  p.编号 as 药品编号,
  p.名称 as 药品名称,
  p.规格 as 规格,
  p.剂型 as 剂型,
  c.名称 as 分类名称,
  COALESCE(SUM(b.当前数量), 0) as 当前库存,
  s.最低库存,
  s.最高库存,
  s.平均成本价,
  COUNT(CASE WHEN b.状态 = 'active' THEN 1 END) as 有效批次数,
  MIN(CASE WHEN b.状态 = 'active' THEN b.有效期 END) as 最近过期日期,
  s.最后入库时间,
  s.最后出库时间
FROM 药品信息 p
LEFT JOIN 药品分类 c ON p.分类编号 = c.编号
LEFT JOIN 药品库存 s ON p.编号 = s.药品编号
LEFT JOIN 药品批次表 b ON p.编号 = b.药品编号 AND b.状态 = 'active'
GROUP BY p.编号, p.名称, p.规格, p.剂型, c.名称, s.最低库存, s.最高库存, s.平均成本价, s.最后入库时间, s.最后出库时间;
```

### 2. 过期预警视图 (过期预警视图)

```sql
CREATE VIEW 过期预警视图 AS
SELECT 
  b.编号 as 批次编号,
  p.编号 as 药品编号,
  p.名称 as 药品名称,
  p.规格 as 规格,
  b.批次号,
  b.有效期,
  b.当前数量,
  b.入库成本价,
  s.名称 as 供应商名称,
  CAST((julianday(b.有效期) - julianday('now')) AS INTEGER) as 剩余天数,
  CASE 
    WHEN julianday(b.有效期) <= julianday('now') THEN '已过期'
    WHEN julianday(b.有效期) <= julianday('now', '+7 days') THEN '7天内过期'
    WHEN julianday(b.有效期) <= julianday('now', '+30 days') THEN '30天内过期'
    WHEN julianday(b.有效期) <= julianday('now', '+90 days') THEN '90天内过期'
    ELSE '正常'
  END as 预警级别
FROM 药品批次表 b
JOIN 药品信息 p ON b.药品编号 = p.编号
LEFT JOIN 供应商 s ON b.供应商编号 = s.编号
WHERE b.状态 = 'active' AND b.当前数量 > 0
ORDER BY b.有效期 ASC;
```

## 索引设计

```sql
-- 药品库存表索引
CREATE INDEX idx_药品库存_药品编号 ON 药品库存(药品编号);
CREATE INDEX idx_药品库存_更新时间 ON 药品库存(更新时间);

-- 药品批次表索引
CREATE INDEX idx_药品批次表_药品编号 ON 药品批次表(药品编号);
CREATE INDEX idx_药品批次表_批次号 ON 药品批次表(批次号);
CREATE INDEX idx_药品批次表_有效期 ON 药品批次表(有效期);
CREATE INDEX idx_药品批次表_状态 ON 药品批次表(状态);

-- 库存变动记录表索引
CREATE INDEX idx_库存变动记录_药品编号 ON 库存变动记录(药品编号);
CREATE INDEX idx_库存变动记录_批次编号 ON 库存变动记录(批次编号);
CREATE INDEX idx_库存变动记录_操作时间 ON 库存变动记录(操作时间);
CREATE INDEX idx_库存变动记录_操作类型 ON 库存变动记录(操作类型);

-- 库存盘点明细表索引
CREATE INDEX idx_库存盘点明细_盘点编号 ON 库存盘点明细(盘点编号);
CREATE INDEX idx_库存盘点明细_药品编号 ON 库存盘点明细(药品编号);
```

## 数据完整性约束

1. **库存一致性**: 药品库存表的当前库存应等于该药品所有有效批次的当前数量之和
2. **批次数量约束**: 批次的当前数量不能超过入库数量
3. **变动记录完整性**: 每次库存变动都必须有对应的变动记录
4. **盘点状态约束**: 盘点进行中时，相关批次应处于锁定状态

## 触发器设计

```sql
-- 批次数量变动时自动更新库存汇总
CREATE TRIGGER update_stock_summary_after_batch_change
AFTER UPDATE OF 当前数量 ON 药品批次表
BEGIN
  UPDATE 药品库存 
  SET 
    当前库存 = (
      SELECT COALESCE(SUM(当前数量), 0) 
      FROM 药品批次表 
      WHERE 药品编号 = NEW.药品编号 AND 状态 = 'active'
    ),
    更新时间 = CURRENT_TIMESTAMP
  WHERE 药品编号 = NEW.药品编号;
END;
```
