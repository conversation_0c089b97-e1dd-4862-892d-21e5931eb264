# UI设计规范

## 概述

本文档定义了药店零售管理系统的完整UI设计标准，包括色彩规范、组件设计、布局标准、交互规范等，确保整个系统的视觉一致性和用户体验统一性。

## 设计原则

### 1. 一致性原则
- 所有界面元素必须遵循统一的视觉风格
- 保持交互行为的一致性
- 确保不同页面间的设计语言统一

### 2. 用户友好原则
- 界面简洁明了，信息层次清晰
- 提供清晰的视觉反馈和状态提示
- 支持高效的操作流程

### 3. 可访问性原则
- 确保良好的色彩对比度
- 支持键盘导航
- 提供适当的焦点管理

## 色彩规范

### 主题色彩系统
```css
/* 主色调 - 蓝色系 */
--primary-50: #eff6ff;
--primary-100: #dbeafe;
--primary-200: #bfdbfe;
--primary-300: #93c5fd;
--primary-400: #60a5fa;
--primary-500: #3b82f6;
--primary-600: #2563eb;
--primary-700: #1d4ed8;  /* 主要文字颜色 */
--primary-800: #1e40af;
--primary-900: #1e3a8a;

/* 中性色 */
--gray-50: #f9fafb;
--gray-100: #f3f4f6;
--gray-200: #e5e7eb;
--gray-300: #d1d5db;
--gray-400: #9ca3af;
--gray-500: #6b7280;
--gray-600: #4b5563;
--gray-700: #374151;
--gray-800: #1f2937;
--gray-900: #111827;

/* 状态色 */
--success-500: #10b981;
--warning-500: #f59e0b;
--error-500: #ef4444;
--info-500: #06b6d4;
```

### 色彩使用规范
- **主要文字**: `text-blue-700` (#1d4ed8) - 用于标题、重要信息
- **次要文字**: `text-gray-600` (#4b5563) - 用于描述性文字
- **表单文字**: `text-black` - 用于表单字段和标签
- **背景色**: `bg-white` - 主要背景色
- **页面背景**: `bg-gray-50` - 页面整体背景
- **边框色**: `border-gray-200` - 统一边框颜色

## 布局规范

### 页面布局结构
```
┌─────────────────────────────────────────┐
│ Header (顶部导航栏)                      │
├─────────────────────────────────────────┤
│ NotificationBar (通知栏)                │
├──────────┬──────────────────────────────┤
│ Sidebar  │ Main Content Area            │
│ (侧边栏) │ (主内容区域)                 │
│          │                              │
│          │                              │
├──────────┴──────────────────────────────┤
│ Footer (页脚)                           │
└─────────────────────────────────────────┘
```

### 尺寸规范
- **侧边栏宽度**: `w-56` (224px)
- **主内容区内边距**: `p-6` (24px)
- **卡片圆角**: `rounded-lg` (8px)
- **按钮圆角**: `rounded-md` (6px)
- **弹窗圆角**: `rounded-xl` (12px)

### 间距规范
- **组件间距**: `gap-6` (24px) - 主要组件间距
- **元素间距**: `gap-3` (12px) - 相关元素间距
- **按钮间距**: `space-x-3` (12px) - 按钮组间距
- **表单间距**: `space-y-4` (16px) - 表单字段间距

## 组件设计规范

### 按钮组件
```css
/* 主要操作按钮 */
.btn-primary {
  @apply px-4 py-2 bg-blue-600 text-white rounded-md 
         hover:bg-blue-700 focus:outline-none focus:ring-2 
         focus:ring-blue-500 transition-colors duration-200;
}

/* 次要操作按钮 */
.btn-secondary {
  @apply px-4 py-2 bg-gray-100 text-gray-700 border 
         border-gray-300 rounded-md hover:bg-gray-200 
         focus:outline-none focus:ring-2 focus:ring-blue-500 
         transition-colors duration-200;
}

/* 危险操作按钮 */
.btn-danger {
  @apply px-4 py-2 bg-red-500 text-white rounded-md 
         hover:bg-red-600 focus:outline-none focus:ring-2 
         focus:ring-red-500 transition-colors duration-200;
}

/* 成功操作按钮 */
.btn-success {
  @apply px-4 py-2 bg-green-500 text-white rounded-md 
         hover:bg-green-600 focus:outline-none focus:ring-2 
         focus:ring-green-500 transition-colors duration-200;
}
```

### 表单组件
```css
/* 输入框 */
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md 
         focus:outline-none focus:ring-2 focus:ring-blue-500 
         focus:border-blue-500 transition-colors duration-200;
}

/* 选择框 */
.form-select {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md 
         bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 
         focus:border-blue-500 transition-colors duration-200;
}

/* 文本域 */
.form-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md 
         focus:outline-none focus:ring-2 focus:ring-blue-500 
         focus:border-blue-500 resize-vertical transition-colors duration-200;
}

/* 标签 */
.form-label {
  @apply block text-sm font-medium text-black mb-1;
}
```

### 卡片组件
```css
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.card-header {
  @apply border-b border-gray-200 pb-4 mb-4;
}

.card-title {
  @apply text-lg font-semibold text-blue-700;
}

.card-content {
  @apply space-y-4;
}
```

## 状态标识规范

### 状态标签样式
```css
/* 成功状态 */
.status-success {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full 
         text-xs font-medium bg-green-100 text-green-800;
}

/* 警告状态 */
.status-warning {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full 
         text-xs font-medium bg-yellow-100 text-yellow-800;
}

/* 错误状态 */
.status-error {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full 
         text-xs font-medium bg-red-100 text-red-800;
}

/* 信息状态 */
.status-info {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full 
         text-xs font-medium bg-blue-100 text-blue-800;
}

/* 默认状态 */
.status-default {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full 
         text-xs font-medium bg-gray-100 text-gray-800;
}
```

### 业务状态映射
- **已完成**: `status-success`
- **处理中**: `status-warning`
- **待处理**: `status-info`
- **已取消**: `status-error`
- **草稿**: `status-default`

## 表格设计规范

### 表格样式
```css
.table-container {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.table {
  @apply min-w-full divide-y divide-gray-200;
}

.table-header {
  @apply bg-gray-50;
}

.table-header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 
         uppercase tracking-wider;
}

.table-body {
  @apply bg-white divide-y divide-gray-200;
}

.table-row {
  @apply hover:bg-gray-50 transition-colors duration-150;
}

.table-cell {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}
```

### 表格操作按钮
```css
.table-action-btn {
  @apply text-blue-600 hover:text-blue-900 text-sm font-medium 
         transition-colors duration-150;
}

.table-action-btn-danger {
  @apply text-red-600 hover:text-red-900 text-sm font-medium 
         transition-colors duration-150;
}
```

## 导航设计规范

### 顶部导航栏
```css
.header {
  @apply flex items-center justify-between h-16 px-6 
         bg-gradient-to-r from-blue-600 to-blue-400 
         text-white shadow-md;
}

.header-logo {
  @apply font-bold text-xl tracking-wide hover:text-white/90 
         transition-colors duration-200 cursor-pointer flex items-center;
}

.header-nav {
  @apply flex gap-6;
}

.header-nav-link {
  @apply hover:underline transition-all duration-200;
}
```

### 侧边栏导航
```css
.sidebar {
  @apply w-56 bg-gray-50 border-r border-gray-200 h-full 
         flex flex-col py-6 px-2 gap-2 text-gray-800;
}

.sidebar-section-title {
  @apply mb-4 font-semibold text-gray-600 px-2;
}

.sidebar-nav-link {
  @apply flex items-center gap-2 px-3 py-2 rounded 
         hover:bg-blue-100 font-medium text-left 
         transition-colors duration-150;
}

.sidebar-nav-link-active {
  @apply bg-blue-100 text-blue-700;
}
```

## 响应式设计规范

### 断点定义
```css
/* 移动设备 */
@media (max-width: 640px) {
  /* sm以下 */
}

/* 平板设备 */
@media (min-width: 641px) and (max-width: 1024px) {
  /* sm到lg */
}

/* 桌面设备 */
@media (min-width: 1025px) {
  /* lg以上 */
}
```

### 响应式布局规则
- **移动端**: 单列布局，侧边栏折叠
- **平板端**: 适当调整间距和字体大小
- **桌面端**: 完整布局，最佳用户体验

## 动画和过渡效果

### 标准过渡时间
- **快速过渡**: `duration-150` (150ms) - 悬停效果
- **标准过渡**: `duration-200` (200ms) - 按钮状态变化
- **慢速过渡**: `duration-300` (300ms) - 弹窗动画

### 常用动画效果
```css
/* 淡入淡出 */
.fade-in {
  @apply transition-opacity duration-200 ease-in-out;
}

/* 滑动效果 */
.slide-in {
  @apply transition-transform duration-300 ease-out;
}

/* 缩放效果 */
.scale-in {
  @apply transition-transform duration-200 ease-out;
}
```

## 图标使用规范

### 图标库
- 使用 Heroicons 作为主要图标库
- 保持图标风格一致性
- 标准图标尺寸: `h-5 w-5` (20px)

### 图标使用场景
- **导航图标**: 侧边栏菜单项
- **操作图标**: 按钮、表格操作
- **状态图标**: 成功、警告、错误提示
- **功能图标**: 搜索、筛选、设置等

## 弹窗设计规范

### 弹窗层级管理
```css
/* 弹窗z-index层级 */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-[10000];
}

.modal-container {
  @apply fixed inset-0 flex items-center justify-center p-4 z-[10001];
}

.modal-content {
  @apply bg-white rounded-xl shadow-xl max-w-md w-full mx-4 z-[10002];
}
```

### 弹窗动画效果
```css
/* 弹窗进入动画 */
.modal-enter {
  @apply opacity-0 scale-95 transition-all duration-300 ease-out;
}

.modal-enter-active {
  @apply opacity-100 scale-100;
}

/* 弹窗退出动画 */
.modal-exit {
  @apply opacity-100 scale-100 transition-all duration-200 ease-in;
}

.modal-exit-active {
  @apply opacity-0 scale-95;
}
```

### 弹窗内容布局
```css
.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-title {
  @apply text-lg font-semibold text-blue-700;
}

.modal-close-btn {
  @apply text-gray-400 hover:text-gray-600 transition-colors duration-150;
}

.modal-body {
  @apply p-6 space-y-4;
}

.modal-footer {
  @apply flex justify-end space-x-3 p-6 border-t border-gray-200;
}
```

## 表单设计规范

### 表单布局
```css
.form-container {
  @apply space-y-6;
}

.form-section {
  @apply space-y-4;
}

.form-section-title {
  @apply text-base font-medium text-blue-700 border-b border-gray-200 pb-2;
}

.form-row {
  @apply grid grid-cols-1 md:grid-cols-2 gap-4;
}

.form-row-full {
  @apply grid grid-cols-1 gap-4;
}
```

### 表单验证样式
```css
/* 正常状态 */
.form-input-normal {
  @apply border-gray-300 focus:border-blue-500 focus:ring-blue-500;
}

/* 错误状态 */
.form-input-error {
  @apply border-red-300 focus:border-red-500 focus:ring-red-500;
}

/* 成功状态 */
.form-input-success {
  @apply border-green-300 focus:border-green-500 focus:ring-green-500;
}

/* 错误提示文字 */
.form-error-text {
  @apply text-sm text-red-600 mt-1;
}

/* 帮助文字 */
.form-help-text {
  @apply text-sm text-gray-500 mt-1;
}
```

### 表单操作区域
```css
.form-actions {
  @apply flex justify-end space-x-3 pt-6 border-t border-gray-200;
}

.form-actions-left {
  @apply flex justify-start space-x-3 pt-6 border-t border-gray-200;
}

.form-actions-between {
  @apply flex justify-between items-center pt-6 border-t border-gray-200;
}
```

## 数据展示规范

### 统计卡片
```css
.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
}

.stat-card-header {
  @apply flex items-center justify-between mb-4;
}

.stat-card-title {
  @apply text-sm font-medium text-gray-600;
}

.stat-card-value {
  @apply text-2xl font-bold text-blue-700;
}

.stat-card-change {
  @apply text-sm font-medium;
}

.stat-card-change-positive {
  @apply text-green-600;
}

.stat-card-change-negative {
  @apply text-red-600;
}
```

### 数据列表
```css
.data-list {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.data-list-header {
  @apply bg-gray-50 px-6 py-3 border-b border-gray-200;
}

.data-list-title {
  @apply text-sm font-medium text-gray-900;
}

.data-list-item {
  @apply px-6 py-4 border-b border-gray-200 last:border-b-0
         hover:bg-gray-50 transition-colors duration-150;
}

.data-list-empty {
  @apply px-6 py-8 text-center text-gray-500;
}
```

## 加载和状态规范

### 加载状态
```css
/* 页面加载 */
.page-loading {
  @apply flex items-center justify-center h-64;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600;
}

.loading-text {
  @apply ml-3 text-gray-600;
}

/* 按钮加载 */
.btn-loading {
  @apply opacity-75 cursor-not-allowed;
}

.btn-loading-spinner {
  @apply animate-spin -ml-1 mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full;
}
```

### 空状态
```css
.empty-state {
  @apply flex flex-col items-center justify-center py-12;
}

.empty-state-icon {
  @apply h-12 w-12 text-gray-400 mb-4;
}

.empty-state-title {
  @apply text-lg font-medium text-gray-900 mb-2;
}

.empty-state-description {
  @apply text-gray-500 text-center max-w-sm;
}

.empty-state-action {
  @apply mt-6;
}
```

## 消息提示规范

### Toast消息
```css
.toast-container {
  @apply fixed top-4 right-4 z-50 space-y-2;
}

.toast {
  @apply max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto
         ring-1 ring-black ring-opacity-5 overflow-hidden;
}

.toast-success {
  @apply border-l-4 border-green-400;
}

.toast-error {
  @apply border-l-4 border-red-400;
}

.toast-warning {
  @apply border-l-4 border-yellow-400;
}

.toast-info {
  @apply border-l-4 border-blue-400;
}
```

### 通知栏
```css
.notification-bar {
  @apply bg-blue-50 border-b border-blue-200 px-4 py-3;
}

.notification-content {
  @apply flex items-center justify-between max-w-7xl mx-auto;
}

.notification-message {
  @apply text-sm text-blue-800;
}

.notification-close {
  @apply text-blue-400 hover:text-blue-600 transition-colors duration-150;
}
```

## 打印样式规范

### 打印媒体查询
```css
@media print {
  /* 隐藏不需要打印的元素 */
  .no-print {
    display: none !important;
  }

  /* 打印页面样式 */
  body {
    font-size: 12pt;
    line-height: 1.4;
    color: black;
    background: white;
  }

  /* 打印表格样式 */
  table {
    border-collapse: collapse;
    width: 100%;
  }

  th, td {
    border: 1px solid #000;
    padding: 4pt;
    text-align: left;
  }
}
```

### 小票打印样式
```css
/* 58mm小票样式 */
.receipt-58mm {
  width: 58mm;
  font-size: 10pt;
  line-height: 1.2;
}

/* 80mm小票样式 */
.receipt-80mm {
  width: 80mm;
  font-size: 11pt;
  line-height: 1.3;
}

/* 112mm小票样式 */
.receipt-112mm {
  width: 112mm;
  font-size: 12pt;
  line-height: 1.4;
}

.receipt-header {
  text-align: center;
  font-weight: bold;
  margin-bottom: 8pt;
}

.receipt-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2pt;
}

.receipt-total {
  border-top: 1px solid #000;
  padding-top: 4pt;
  margin-top: 8pt;
  font-weight: bold;
}
```

## 可访问性规范

### 焦点管理
```css
/* 焦点样式 */
.focus-visible {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* 跳过链接 */
.skip-link {
  @apply sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0
         bg-blue-600 text-white px-4 py-2 z-50;
}
```

### 屏幕阅读器支持
```css
/* 仅屏幕阅读器可见 */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap
         border-0 clip-rect(0,0,0,0);
}

/* 屏幕阅读器隐藏 */
.sr-hidden {
  @apply aria-hidden;
}
```

## 更新记录

- **2025-07-05**: 初始版本创建
- 建立了完整的UI设计规范体系
- 定义了色彩、布局、组件等设计标准
- 提供了响应式设计和动画效果规范
- 添加了弹窗、表单、数据展示等详细规范
- 完善了加载状态、消息提示、打印样式等规范
- 增加了可访问性设计规范
