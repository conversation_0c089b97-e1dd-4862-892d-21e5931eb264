import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

/**
 * 简化的库存管理数据库结构创建
 */

export async function POST(request: NextRequest) {
  try {
    console.log('开始创建库存管理表结构...');

    // 1. 创建药品库存主表
    console.log('创建药品库存主表...');
    await run(`
      CREATE TABLE IF NOT EXISTS 药品库存 (
        编号 INTEGER PRIMARY KEY AUTOINCREMENT,
        药品编号 INTEGER NOT NULL UNIQUE,
        当前库存 INTEGER NOT NULL DEFAULT 0,
        最低库存 INTEGER DEFAULT 0,
        最高库存 INTEGER DEFAULT 0,
        平均成本价 DECIMAL(10,2) DEFAULT 0,
        最后入库时间 DATETIME,
        最后出库时间 DATETIME,
        创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
        更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 2. 创建库存变动记录表
    console.log('创建库存变动记录表...');
    await run(`
      CREATE TABLE IF NOT EXISTS 库存变动记录 (
        编号 INTEGER PRIMARY KEY AUTOINCREMENT,
        药品编号 INTEGER NOT NULL,
        批次编号 INTEGER,
        操作类型 TEXT NOT NULL CHECK (操作类型 IN ('入库', '出库', '盘点', '调整', '过期处理')),
        变动数量 INTEGER NOT NULL,
        变动前数量 INTEGER NOT NULL DEFAULT 0,
        变动后数量 INTEGER NOT NULL DEFAULT 0,
        单价 DECIMAL(10,2),
        总金额 DECIMAL(10,2),
        供应商编号 INTEGER,
        关联单据类型 TEXT CHECK (关联单据类型 IN ('采购单', '销售单', '盘点单', '调整单', '报损单')),
        关联单据编号 TEXT,
        操作人 TEXT,
        操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
        备注 TEXT,
        码上放心单据号 TEXT,
        码上放心上传状态 TEXT CHECK (码上放心上传状态 IN ('pending', 'success', 'failed')),
        码上放心上传时间 DATETIME,
        码上放心响应 TEXT
      )
    `);

    // 3. 创建库存盘点表
    console.log('创建库存盘点表...');
    await run(`
      CREATE TABLE IF NOT EXISTS 库存盘点 (
        编号 INTEGER PRIMARY KEY AUTOINCREMENT,
        盘点编号 TEXT UNIQUE NOT NULL,
        盘点名称 TEXT NOT NULL,
        盘点类型 TEXT CHECK (盘点类型 IN ('全盘', '抽盘', '动态盘点')) DEFAULT '全盘',
        盘点状态 TEXT CHECK (盘点状态 IN ('进行中', '已完成', '已取消')) DEFAULT '进行中',
        开始时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
        完成时间 DATETIME,
        盘点人 TEXT,
        审核人 TEXT,
        备注 TEXT,
        创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 4. 创建库存盘点明细表
    console.log('创建库存盘点明细表...');
    await run(`
      CREATE TABLE IF NOT EXISTS 库存盘点明细 (
        编号 INTEGER PRIMARY KEY AUTOINCREMENT,
        盘点编号 INTEGER NOT NULL,
        药品编号 INTEGER NOT NULL,
        批次编号 INTEGER,
        账面数量 INTEGER NOT NULL DEFAULT 0,
        实盘数量 INTEGER,
        差异数量 INTEGER DEFAULT 0,
        差异原因 TEXT,
        盘点状态 TEXT CHECK (盘点状态 IN ('待盘点', '已盘点', '有差异', '已处理')) DEFAULT '待盘点',
        盘点时间 DATETIME,
        备注 TEXT
      )
    `);

    // 5. 创建过期预警配置表
    console.log('创建过期预警配置表...');
    await run(`
      CREATE TABLE IF NOT EXISTS 过期预警配置 (
        编号 INTEGER PRIMARY KEY AUTOINCREMENT,
        药品编号 INTEGER,
        药品分类编号 INTEGER,
        预警天数 INTEGER NOT NULL DEFAULT 30,
        预警级别 TEXT CHECK (预警级别 IN ('低', '中', '高')) DEFAULT '中',
        是否启用 BOOLEAN DEFAULT 1,
        创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
        更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // 6. 创建基本索引
    console.log('创建索引...');
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_药品库存_药品编号 ON 药品库存(药品编号)',
      'CREATE INDEX IF NOT EXISTS idx_库存变动记录_药品编号 ON 库存变动记录(药品编号)',
      'CREATE INDEX IF NOT EXISTS idx_库存变动记录_操作时间 ON 库存变动记录(操作时间)',
      'CREATE INDEX IF NOT EXISTS idx_库存盘点明细_盘点编号 ON 库存盘点明细(盘点编号)'
    ];

    for (const indexSql of indexes) {
      await run(indexSql);
    }

    // 7. 初始化药品库存数据
    console.log('初始化药品库存数据...');
    await run(`
      INSERT OR IGNORE INTO 药品库存 (药品编号, 当前库存, 最低库存, 创建时间)
      SELECT 
        编号,
        0 as 当前库存,
        0 as 最低库存,
        CURRENT_TIMESTAMP
      FROM 药品信息
    `);

    console.log('库存管理表结构创建完成');

    return NextResponse.json({
      success: true,
      message: '库存管理表结构创建成功',
      data: {
        tables_created: [
          '药品库存',
          '库存变动记录',
          '库存盘点',
          '库存盘点明细',
          '过期预警配置'
        ],
        indexes_created: 4
      }
    });

  } catch (error) {
    console.error('库存管理表结构创建失败:', error);

    return NextResponse.json(
      {
        success: false,
        message: '库存管理表结构创建失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 获取状态
export async function GET() {
  try {
    const tables = ['药品库存', '库存变动记录', '库存盘点', '库存盘点明细', '过期预警配置'];
    const tableStatus = {};

    for (const tableName of tables) {
      const tableExists = await query(`SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}'`);
      tableStatus[tableName] = tableExists && tableExists.length > 0;
    }

    return NextResponse.json({
      success: true,
      data: {
        tables: tableStatus,
        allTablesExist: Object.values(tableStatus).every(exists => exists)
      }
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        message: '获取状态失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
