import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tableName = searchParams.get('table');

    if (!tableName) {
      return NextResponse.json(
        { success: false, message: '请提供表名' },
        { status: 400 }
      );
    }

    // 获取表结构
    const tableInfo = await query(`PRAGMA table_info(${tableName})`);
    
    return NextResponse.json({
      success: true,
      data: {
        tableName,
        columns: tableInfo
      }
    });
  } catch (error) {
    console.error('获取表结构失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取表结构失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
