# 订单管理功能需求分析与设计

## 1. 系统现状分析

### 1.1 现有订单类型

根据代码分析，系统中存在以下订单类型：

#### A. 销售订单 (销售订单表)
**表结构：**
```sql
销售订单 (
  id INTEGER PRIMARY KEY,
  order_number TEXT,           -- 订单编号
  customer_id INTEGER,         -- 客户ID
  operator_id INTEGER,         -- 操作员ID
  total_amount DECIMAL,        -- 总金额
  discount_amount DECIMAL,     -- 折扣金额
  payable_amount DECIMAL,      -- 应付金额
  received_amount DECIMAL,     -- 实收金额
  change_amount DECIMAL,       -- 找零金额
  payment_method TEXT,         -- 支付方式
  status TEXT,                 -- 订单状态
  note TEXT,                   -- 备注
  created_at DATETIME          -- 创建时间
)
```

**关联表：**
- `订单明细` - 订单商品明细
- `product_batches` - 药品批次信息

#### B. 库存记录 (入库/出库订单)
**表结构：**
```sql
库存记录 (
  编号 INTEGER PRIMARY KEY,
  药品编号 INTEGER,
  操作类型 TEXT,              -- '入库', '出库', '盘点', '调整'
  数量变化 INTEGER,
  操作前库存 INTEGER,
  操作后库存 INTEGER,
  供应商编号 INTEGER,
  批次号 TEXT,
  有效期 DATE,
  成本价 DECIMAL,
  操作人 TEXT,
  操作时间 DATETIME,
  备注 TEXT,
  码上放心单据号 TEXT,
  码上放心上传状态 TEXT,
  码上放心上传时间 DATETIME,
  码上放心响应 TEXT
)
```

**关联表：**
- `药品追溯码记录` - 追溯码信息

### 1.2 订单状态定义

#### 销售订单状态
- `pending` - 待处理
- `processing` - 处理中
- `completed` - 已完成
- `cancelled` - 已取消
- `草稿` - 草稿状态

#### 库存操作状态
- `入库` - 药品入库
- `出库` - 药品出库
- `盘点` - 库存盘点
- `调整` - 库存调整

## 2. 统一订单管理设计

### 2.1 订单类型分类

为了统一管理，将所有订单分为以下类型：

```typescript
enum OrderType {
  SALES = 'sales',           // 销售订单
  STOCK_IN = 'stock_in',     // 入库订单
  STOCK_OUT = 'stock_out',   // 出库订单
  INVENTORY = 'inventory',   // 盘点订单
  ADJUSTMENT = 'adjustment'  // 调整订单
}
```

### 2.2 统一订单数据结构

```typescript
interface UnifiedOrder {
  id: string;
  orderNumber: string;
  orderType: OrderType;
  status: string;
  
  // 时间信息
  createdAt: string;
  updatedAt?: string;
  operationDate: string;      // 操作日期
  
  // 金额信息（仅销售订单）
  totalAmount?: number;
  discountAmount?: number;
  payableAmount?: number;
  receivedAmount?: number;
  
  // 操作信息
  operator: string;
  note?: string;
  
  // 关联信息
  customerId?: number;
  customerName?: string;
  supplierId?: number;
  supplierName?: string;
  
  // 商品信息
  items: OrderItem[];
  
  // 扩展信息
  batchNumber?: string;
  traceCodesCount?: number;
  paymentMethod?: string;
}

interface OrderItem {
  id: string;
  productId: number;
  productName: string;
  productBarcode?: string;
  specification?: string;
  quantity: number;
  unitPrice?: number;
  subtotal?: number;
  batchNumber?: string;
  expiryDate?: string;
}
```

### 2.3 筛选功能设计

#### 订单类型筛选
```typescript
interface OrderTypeFilter {
  sales: boolean;      // 销售订单
  stockIn: boolean;    // 入库订单
  stockOut: boolean;   // 出库订单
  inventory: boolean;  // 盘点订单
  adjustment: boolean; // 调整订单
}
```

#### 日期筛选
```typescript
interface DateFilter {
  startDate: string;   // 开始日期
  endDate: string;     // 结束日期
  quickSelect: 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom';
}
```

#### 状态筛选
```typescript
interface StatusFilter {
  pending: boolean;    // 待处理
  processing: boolean; // 处理中
  completed: boolean;  // 已完成
  cancelled: boolean;  // 已取消
  draft: boolean;      // 草稿
}
```

#### 金额筛选
```typescript
interface AmountFilter {
  minAmount?: number;  // 最小金额
  maxAmount?: number;  // 最大金额
}
```

### 2.4 搜索功能设计

#### 搜索字段
- 订单编号
- 药品名称
- 药品条形码
- 批次号
- 客户姓名
- 客户电话
- 供应商名称
- 操作人员

#### 搜索类型
```typescript
interface SearchConfig {
  keyword: string;
  searchType: 'all' | 'orderNumber' | 'productName' | 'barcode' | 'batchNumber' | 'customer' | 'supplier';
  fuzzyMatch: boolean;  // 是否模糊匹配
}
```

## 3. 数据库查询设计

### 3.1 统一查询API设计

```sql
-- 销售订单查询
SELECT 
  'sales' as order_type,
  id,
  order_number,
  status,
  total_amount,
  discount_amount,
  payable_amount,
  received_amount,
  payment_method,
  created_at,
  note,
  (SELECT name FROM customers WHERE id = customer_id) as customer_name
FROM 销售订单
WHERE created_at BETWEEN ? AND ?

UNION ALL

-- 库存记录查询
SELECT 
  CASE 操作类型
    WHEN '入库' THEN 'stock_in'
    WHEN '出库' THEN 'stock_out'
    WHEN '盘点' THEN 'inventory'
    WHEN '调整' THEN 'adjustment'
  END as order_type,
  编号 as id,
  COALESCE(码上放心单据号, '系统生成-' || 编号) as order_number,
  '已完成' as status,
  NULL as total_amount,
  NULL as discount_amount,
  NULL as payable_amount,
  NULL as received_amount,
  NULL as payment_method,
  操作时间 as created_at,
  备注 as note,
  NULL as customer_name
FROM 库存记录
WHERE 操作时间 BETWEEN ? AND ?

ORDER BY created_at DESC
```

### 3.2 搜索查询优化

```sql
-- 创建搜索索引
CREATE INDEX IF NOT EXISTS idx_sales_order_number ON 销售订单(order_number);
CREATE INDEX IF NOT EXISTS idx_sales_created_at ON 销售订单(created_at);
CREATE INDEX IF NOT EXISTS idx_inventory_operation_time ON 库存记录(操作时间);
CREATE INDEX IF NOT EXISTS idx_inventory_batch_number ON 库存记录(批次号);
CREATE INDEX IF NOT EXISTS idx_product_name ON 药品信息(名称);
CREATE INDEX IF NOT EXISTS idx_product_barcode ON 药品信息(条形码);
```

## 4. 前端界面设计

### 4.1 页面布局

```
┌─────────────────────────────────────────────────────────┐
│ 订单管理                                                │
├─────────────────────────────────────────────────────────┤
│ [筛选面板]                                              │
│ 类型: □销售 □入库 □出库 □盘点 □调整                    │
│ 日期: [今日▼] [2025-06-29] 至 [2025-06-29]             │
│ 状态: □待处理 □处理中 □已完成 □已取消                  │
│ 搜索: [搜索框________________] [搜索▼]                   │
├─────────────────────────────────────────────────────────┤
│ [订单列表]                                              │
│ 订单号    类型  状态  金额    时间      操作            │
│ SO-001   销售  已完成 ¥128.50 06-29 10:30 [查看][编辑] │
│ RK-001   入库  已完成   -     06-29 09:15 [查看]       │
│ CK-001   出库  已完成   -     06-29 08:45 [查看]       │
├─────────────────────────────────────────────────────────┤
│ [分页控件] 共123条 第1/13页 [上一页][1][2][3][下一页]   │
└─────────────────────────────────────────────────────────┘
```

### 4.2 响应式设计

#### 桌面端 (≥1024px)
- 完整的筛选面板
- 表格形式显示订单列表
- 每行显示完整信息

#### 平板端 (768px-1023px)
- 折叠式筛选面板
- 卡片形式显示订单
- 关键信息优先显示

#### 移动端 (<768px)
- 抽屉式筛选面板
- 紧凑的卡片布局
- 滑动查看更多信息

## 5. 性能优化策略

### 5.1 数据分页
- 默认每页20条记录
- 支持10/20/50/100条切换
- 虚拟滚动（大数据量时）

### 5.2 查询优化
- 索引优化
- 分页查询
- 缓存常用查询结果
- 异步加载详情数据

### 5.3 前端优化
- 组件懒加载
- 虚拟列表
- 防抖搜索
- 状态管理优化

## 6. 扩展功能设计

### 6.1 批量操作
- 批量导出
- 批量状态更新
- 批量删除（软删除）

### 6.2 统计分析
- 订单数量统计
- 金额统计
- 趋势分析
- 可视化图表

### 6.3 权限控制
- 角色权限管理
- 数据访问控制
- 操作日志记录

## 7. 技术实现要点

### 7.1 API设计
- RESTful API规范
- 统一的响应格式
- 错误处理机制
- 接口文档

### 7.2 数据一致性
- 事务处理
- 数据校验
- 并发控制
- 备份恢复

### 7.3 安全性
- 输入验证
- SQL注入防护
- XSS防护
- 访问控制

这个设计为订单管理功能提供了完整的技术架构和实现方案，确保能够统一管理所有类型的订单，并提供强大的筛选和搜索功能。
