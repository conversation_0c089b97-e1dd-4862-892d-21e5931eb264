import { NextRequest, NextResponse } from 'next/server';

/**
 * 测试追溯码扫描API - 用于开发测试
 * POST /api/sales/test-trace-scan
 */
export async function POST(request: NextRequest) {
  try {
    const { traceCode } = await request.json();

    if (!traceCode) {
      return NextResponse.json({
        success: false,
        message: '请提供追溯码'
      }, { status: 400 });
    }

    // 模拟不同的测试场景
    if (traceCode === 'TEST_USED') {
      return NextResponse.json({
        success: false,
        message: '该追溯码已被使用（出库），操作时间：2025-07-08 10:30:00',
        data: {
          isUsed: true
        }
      }, { status: 400 });
    }

    if (traceCode === 'TEST_NOT_FOUND') {
      return NextResponse.json({
        success: false,
        message: '在本地数据库中未找到对应的药品，请先在药品管理中添加该药品',
        data: {
          traceCodeInfo: {
            batchNo: 'TEST20250708',
            expireDate: '2026-07-08',
            manufacturer: '测试制药有限公司',
            drugName: '测试药品',
            specification: '10mg*30片',
            approvalNumber: 'TEST2025S001'
          }
        }
      }, { status: 404 });
    }

    // 模拟成功的追溯码扫描
    return NextResponse.json({
      success: true,
      message: '追溯码扫描成功',
      data: {
        traceCode,
        localProduct: {
          id: 1,
          name: '阿莫西林胶囊',
          genericName: '阿莫西林',
          specification: '0.25g*24粒',
          manufacturer: '华北制药股份有限公司',
          price: 12.50,
          stockQuantity: 100,
          barcode: '6901234567890',
          approvalNumber: 'H20051234'
        },
        batchInfo: {
          batchNo: '20250601001',
          expireDate: '2027-06-01',
          productionDate: '2025-06-01',
          manufacturer: '华北制药股份有限公司',
          drugName: '阿莫西林胶囊',
          specification: '0.25g*24粒',
          approvalNumber: 'H20051234',
          barcode: '6901234567890',
          localBatchId: 1,
          localBatchQuantity: 50
        },
        canSell: true,
        quantity: 1
      }
    });

  } catch (error) {
    console.error('测试追溯码扫描失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '扫描追溯码失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
