import { run, query } from './db';
import { ensureCategoriesData } from './db-init-categories';

/**
 * 检查并创建"系统设置"表
 * @returns {Promise<boolean>} 是否成功创建或已存在表
 */
export async function ensureSettingsTable(): Promise<boolean> {
  try {
    // 检查"系统设置"表是否存在
    const tableExists = await query(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='系统设置'"
    );

    if (!tableExists || tableExists.length === 0) {
      console.log('系统设置 表不存在，正在创建...');

      // 创建"系统设置"表
      await run(`
        CREATE TABLE IF NOT EXISTS 系统设置 (
          编号 INTEGER PRIMARY KEY AUTOINCREMENT,
          设置名称 TEXT NOT NULL UNIQUE,
          设置值 TEXT,
          描述 TEXT,
          创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);

      // 添加默认设置
      await addDefaultSettings();

      console.log('系统设置 表创建成功');
      return true;
    }

    // 检查是否有码上放心开放平台设置项
    const hasMashangfangxinSettings = await query(
      "SELECT COUNT(*) as count FROM 系统设置 WHERE 设置名称 = '码上放心开放平台AppKey'"
    );

    if (!hasMashangfangxinSettings || hasMashangfangxinSettings[0]?.count === 0) {
      console.log('添加码上放心开放平台设置项...');
      await addMashangfangxinSettings();
    }

    // 检查是否有小票尺寸设置项
    const hasReceiptSizeSettings = await query(
      "SELECT COUNT(*) as count FROM 系统设置 WHERE 设置名称 = '小票尺寸'"
    );

    if (!hasReceiptSizeSettings || hasReceiptSizeSettings[0]?.count === 0) {
      console.log('添加小票尺寸设置项...');
      await run(
        'INSERT INTO 系统设置 (设置名称, 设置值, 描述) VALUES (?, ?, ?)',
        ['小票尺寸', '80mm', '小票打印尺寸设置']
      );
    }

    // 确保药品分类数据存在
    try {
      await ensureCategoriesData();
    } catch (error) {
      console.error('初始化药品分类数据失败:', error);
      // 继续执行，不影响其他功能
    }

    // 确保操作日志表存在
    try {
      await ensureAuditLogTable();
    } catch (error) {
      console.error('初始化操作日志表失败:', error);
      // 继续执行，不影响其他功能
    }

    return true;
  } catch (error) {
    console.error('确保系统设置表存在时出错:', error);
    return false;
  }
}

/**
 * 添加默认设置
 */
async function addDefaultSettings(): Promise<void> {
  try {
    // 添加基本设置
    await run(
      'INSERT INTO 系统设置 (设置名称, 设置值, 描述) VALUES (?, ?, ?)',
      ['药店名称', '药店零售管理系统', '药店名称']
    );

    await run(
      'INSERT INTO 系统设置 (设置名称, 设置值, 描述) VALUES (?, ?, ?)',
      ['订单编号位数', '4', '订单编号位数']
    );

    // 添加小票设置
    await run(
      'INSERT INTO 系统设置 (设置名称, 设置值, 描述) VALUES (?, ?, ?)',
      ['小票尺寸', '80mm', '小票打印尺寸设置']
    );

    // 添加码上放心开放平台设置
    await addMashangfangxinSettings();

    console.log('默认设置添加成功');
  } catch (error) {
    console.error('添加默认设置失败:', error);
    throw error;
  }
}

/**
 * 添加码上放心开放平台设置
 */
async function addMashangfangxinSettings(): Promise<void> {
  try {
    // 添加码上放心开放平台设置
    await run(
      'INSERT INTO 系统设置 (设置名称, 设置值, 描述) VALUES (?, ?, ?)',
      ['码上放心开放平台AppKey', '', '码上放心开放平台AppKey']
    );

    await run(
      'INSERT INTO 系统设置 (设置名称, 设置值, 描述) VALUES (?, ?, ?)',
      ['码上放心开放平台AppSecret', '', '码上放心开放平台AppSecret']
    );

    await run(
      'INSERT INTO 系统设置 (设置名称, 设置值, 描述) VALUES (?, ?, ?)',
      ['码上放心开放平台Url', 'http://gw.api.taobao.com/router/rest', '码上放心开放平台API URL']
    );

    await run(
      'INSERT INTO 系统设置 (设置名称, 设置值, 描述) VALUES (?, ?, ?)',
      ['码上放心开放平台RefEntId', '', '码上放心开放平台企业ID']
    );

    console.log('码上放心开放平台设置添加成功');
  } catch (error) {
    console.error('添加码上放心开放平台设置失败:', error);
    throw error;
  }
}

/**
 * 确保操作日志表存在
 */
async function ensureAuditLogTable(): Promise<void> {
  try {
    // 检查操作日志表是否存在
    const tableExists = await query(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='操作日志'"
    );

    if (!tableExists || tableExists.length === 0) {
      console.log('操作日志表不存在，正在创建...');

      // 创建操作日志表
      await run(`
        CREATE TABLE IF NOT EXISTS 操作日志 (
          编号 INTEGER PRIMARY KEY AUTOINCREMENT,
          用户编号 INTEGER NOT NULL DEFAULT 1,
          用户名 TEXT NOT NULL DEFAULT '系统管理员',
          操作类型 TEXT NOT NULL,
          操作对象 TEXT NOT NULL,
          操作详情 TEXT,
          IP地址 TEXT,
          用户代理 TEXT,
          操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
          操作结果 TEXT DEFAULT 'success'
        )
      `);

      // 创建索引
      await run(`
        CREATE INDEX IF NOT EXISTS idx_audit_log_time ON 操作日志(操作时间)
      `);

      await run(`
        CREATE INDEX IF NOT EXISTS idx_audit_log_user ON 操作日志(用户编号)
      `);

      await run(`
        CREATE INDEX IF NOT EXISTS idx_audit_log_action ON 操作日志(操作类型)
      `);

      console.log('操作日志表创建成功');
    }
  } catch (error) {
    console.error('创建操作日志表失败:', error);
    throw error;
  }
}
