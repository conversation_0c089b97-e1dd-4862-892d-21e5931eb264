'use client';

import { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';

interface Product {
  id: number;
  name: string;
  stock_quantity: number;
  dosage_form: string;
  storage_condition: string;
}

interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
}

interface InventoryCountProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
}

interface CountItem {
  product_id: number;
  product_name: string;
  system_quantity: number;
  actual_quantity: number;
  difference: number;
  notes: string;
  dosage_form: string;
  storage_condition: string;
}

export default function InventoryCount({ isOpen, onClose, onSubmit }: InventoryCountProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [countItems, setCountItems] = useState<CountItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [categories, setCategories] = useState<{id: number, name: string}[]>([]);
  const [currentTab, setCurrentTab] = useState<'all' | 'discrepancy'>('all');
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      fetchProducts();
      fetchCategories();
    }
  }, [isOpen]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/products');
      const data = await response.json() as ApiResponse<Product[]>;
      if (data.success && data.data) {
        const products = data.data;
        setProducts(products);

        // 初始化盘点项目
        const items = products.map((p: Product) => ({
          product_id: p.id,
          product_name: p.name,
          system_quantity: p.stock_quantity,
          actual_quantity: p.stock_quantity, // 默认与系统数量相同
          difference: 0,
          notes: '',
          dosage_form: p.dosage_form,
          storage_condition: p.storage_condition
        }));
        setCountItems(items);
      }
    } catch (error) {
      console.error('获取药品数据失败:', error);
      setErrorMessage('获取药品数据失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');
      const data = await response.json() as ApiResponse<{id: number, name: string}[]>;
      if (data.success && data.data) {
        setCategories(data.data);
      }
    } catch (error) {
      console.error('获取分类数据失败:', error);
      setErrorMessage('获取分类数据失败，请重试');
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  const handleActualQuantityChange = (productId: number, value: string) => {
    const actualQuantity = parseInt(value) || 0;

    setCountItems(prevItems =>
      prevItems.map(item => {
        if (item.product_id === productId) {
          const difference = actualQuantity - item.system_quantity;
          return { ...item, actual_quantity: actualQuantity, difference };
        }
        return item;
      })
    );
  };

  const handleNotesChange = (productId: number, value: string) => {
    setCountItems(prevItems =>
      prevItems.map(item => {
        if (item.product_id === productId) {
          return { ...item, notes: value };
        }
        return item;
      })
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 只提交有差异的项目
    const itemsToSubmit = countItems.filter(item => item.difference !== 0);

    if (itemsToSubmit.length === 0) {
      setErrorMessage('没有发现任何库存差异，不需要调整');
      return;
    }

    try {
      setLoading(true);
      await onSubmit({
        items: itemsToSubmit,
        count_date: new Date().toISOString().split('T')[0],
        notes: '库存盘点调整'
      });
    } catch (error) {
      console.error('提交盘点数据失败:', error);
      setErrorMessage('提交盘点数据失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 过滤显示的项目
  const filteredItems = countItems.filter(item => {
    const matchesSearch =
      (item.product_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (item.dosage_form?.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = !selectedCategory ||
      products.find(p => p.id === item.product_id)?.id.toString() === selectedCategory;

    const matchesTab = currentTab === 'all' || (currentTab === 'discrepancy' && item.difference !== 0);

    return matchesSearch && matchesCategory && matchesTab;
  });

  return (
    <Dialog open={isOpen} onClose={onClose} className="relative z-50">
      <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="mx-auto max-w-5xl w-full bg-gray-50 rounded-xl shadow-lg border border-gray-200 max-h-[90vh] flex flex-col">
          <div className="p-6 border-b border-gray-200">
            <Dialog.Title className="text-xl font-medium leading-6 text-gray-800">
              库存盘点
            </Dialog.Title>
          </div>

          <div className="p-6 flex-grow overflow-auto">
            {errorMessage && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 text-red-700 rounded-md">
                {errorMessage}
                <button
                  className="float-right font-bold"
                  onClick={() => setErrorMessage(null)}
                >
                  ×
                </button>
              </div>
            )}

            <div className="mb-6 flex flex-wrap gap-4">
              <div className="flex-1 min-w-[250px]">
                <input
                  type="text"
                  placeholder="搜索药品名称或剂型..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                />
              </div>
              <div className="w-48">
                <select
                  value={selectedCategory}
                  onChange={handleCategoryChange}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                >
                  <option value="">全部分类</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex rounded-md overflow-hidden border border-gray-300">
                <button
                  type="button"
                  className={`px-4 py-2 text-sm font-medium ${currentTab === 'all' ? 'bg-blue-500 text-white' : 'bg-white text-gray-700'}`}
                  onClick={() => setCurrentTab('all')}
                >
                  全部药品
                </button>
                <button
                  type="button"
                  className={`px-4 py-2 text-sm font-medium ${currentTab === 'discrepancy' ? 'bg-blue-500 text-white' : 'bg-white text-gray-700'}`}
                  onClick={() => setCurrentTab('discrepancy')}
                >
                  仅显示差异
                </button>
              </div>
            </div>

            {loading ? (
              <div className="flex justify-center my-8">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            ) : filteredItems.length > 0 ? (
              <form onSubmit={handleSubmit}>
                <div className="overflow-hidden rounded-lg border border-gray-200">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-100">
                      <tr>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4">药品名称</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">剂型</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">系统库存</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">实际库存</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">差异</th>
                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">备注</th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {filteredItems.map(item => (
                        <tr key={item.product_id} className={item.difference !== 0 ? 'bg-yellow-50' : ''}>
                          <td className="px-4 py-3 text-sm text-gray-900">{item.product_name}</td>
                          <td className="px-4 py-3 text-sm text-gray-500">{item.dosage_form}</td>
                          <td className="px-4 py-3 text-sm text-gray-500">{item.system_quantity}</td>
                          <td className="px-4 py-3">
                            <input
                              type="number"
                              min="0"
                              value={item.actual_quantity}
                              onChange={(e) => handleActualQuantityChange(item.product_id, e.target.value)}
                              className="w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          </td>
                          <td className={`px-4 py-3 text-sm font-medium ${item.difference > 0 ? 'text-green-600' : item.difference < 0 ? 'text-red-600' : 'text-gray-500'}`}>
                            {item.difference > 0 ? '+' : ''}{item.difference}
                          </td>
                          <td className="px-4 py-3">
                            <input
                              type="text"
                              placeholder="原因备注..."
                              value={item.notes}
                              onChange={(e) => handleNotesChange(item.product_id, e.target.value)}
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>

                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-50 border border-gray-300 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    className="px-5 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    disabled={loading || countItems.every(item => item.difference === 0)}
                  >
                    {loading ? '保存中...' : '保存'}
                  </button>
                </div>
              </form>
            ) : (
              <div className="text-center py-8 text-gray-500">
                没有找到符合条件的药品
              </div>
            )}
          </div>
        </Dialog.Panel>
      </div>
    </Dialog>
  );
}