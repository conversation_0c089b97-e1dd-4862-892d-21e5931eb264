'use client';

import React, { useState, useEffect } from 'react';

interface BatchNumberInputProps {
  value: string;
  onChange: (value: string, source: 'manual' | 'auto') => void;
  productId?: number;
  disabled?: boolean;
  className?: string;
}

interface BatchTypeOption {
  value: string;
  label: string;
  description: string;
}

const batchTypes: BatchTypeOption[] = [
  { value: 'PC', label: '采购批次', description: '从供应商采购的药品批次' },
  { value: 'PR', label: '生产批次', description: '自主生产的药品批次' },
  { value: 'RT', label: '退货批次', description: '退货处理的药品批次' },
  { value: 'AD', label: '调整批次', description: '库存调整的药品批次' },
  { value: 'TR', label: '调拨批次', description: '门店间调拨的药品批次' },
  { value: 'QC', label: '质检批次', description: '质量检验的药品批次' }
];

export default function BatchNumberInput({ 
  value, 
  onChange, 
  productId, 
  disabled = false,
  className = ''
}: BatchNumberInputProps) {
  const [isAutoMode, setIsAutoMode] = useState(true); // 默认启用自动生成
  const [selectedBatchType, setSelectedBatchType] = useState('PC');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedBatch, setGeneratedBatch] = useState('');
  const [batchInfo, setBatchInfo] = useState<any>(null);

  // 自动生成批次号
  const generateBatchNumber = async () => {
    if (!productId) {
      console.warn('请先选择药品');
      return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch(`/api/inventory/batch-number?type=${selectedBatchType}&product_id=${productId}`);
      const data = await response.json();
      
      if (data.success) {
        const newBatchNumber = data.data.batchNumber;
        setGeneratedBatch(newBatchNumber);
        setBatchInfo(data.data);
        onChange(newBatchNumber, 'auto');
      } else {
        console.error('生成批次号失败：' + data.message);
      }
    } catch (error) {
      console.error('生成批次号失败:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // 切换模式
  const handleModeChange = (autoMode: boolean) => {
    setIsAutoMode(autoMode);
    if (autoMode) {
      generateBatchNumber();
    } else {
      onChange('', 'manual');
      setGeneratedBatch('');
      setBatchInfo(null);
    }
  };

  // 手动输入变化
  const handleManualChange = (newValue: string) => {
    onChange(newValue, 'manual');
  };

  // 批次类型变化时重新生成
  useEffect(() => {
    if (isAutoMode && productId) {
      generateBatchNumber();
    }
  }, [selectedBatchType, productId]);

  // 组件挂载时如果是自动模式且有产品ID，自动生成批次号
  useEffect(() => {
    if (isAutoMode && productId && !value) {
      generateBatchNumber();
    }
  }, [isAutoMode, productId]);

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 模式选择 */}
      <div className="flex items-center space-x-4">
        <label className="flex items-center">
          <input
            type="radio"
            name="batchMode"
            checked={!isAutoMode}
            onChange={() => handleModeChange(false)}
            disabled={disabled}
            className="mr-2"
          />
          <span className="text-sm text-blue-700">手动输入批次号</span>
        </label>
        <label className="flex items-center">
          <input
            type="radio"
            name="batchMode"
            checked={isAutoMode}
            onChange={() => handleModeChange(true)}
            disabled={disabled}
            className="mr-2"
          />
          <span className="text-sm text-blue-700">自动生成批次号</span>
        </label>
      </div>

      {/* 手动输入模式 */}
      {!isAutoMode && (
        <div>
          <input
            type="text"
            value={value}
            onChange={(e) => handleManualChange(e.target.value)}
            placeholder="请输入批次号"
            disabled={disabled}
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
          />
          <p className="mt-1 text-xs text-gray-500">
            支持字母、数字、连字符、下划线和中文字符，长度不超过50个字符
          </p>
        </div>
      )}

      {/* 自动生成模式 */}
      {isAutoMode && (
        <div className="space-y-3">
          {/* 批次类型选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              批次类型
            </label>
            <select
              value={selectedBatchType}
              onChange={(e) => setSelectedBatchType(e.target.value)}
              disabled={disabled || isGenerating}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
            >
              {batchTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label} ({type.value})
                </option>
              ))}
            </select>
            <p className="mt-1 text-xs text-gray-500">
              {batchTypes.find(t => t.value === selectedBatchType)?.description}
            </p>
          </div>

          {/* 生成的批次号显示 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              生成的批次号
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={value}
                readOnly
                disabled={disabled}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-blue-700 font-mono"
                placeholder={isGenerating ? "正在生成..." : "点击生成按钮"}
              />
              <button
                type="button"
                onClick={generateBatchNumber}
                disabled={disabled || isGenerating || !productId}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? '生成中...' : '重新生成'}
              </button>
            </div>
          </div>

          {/* 批次号信息 */}
          {batchInfo && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
              <h4 className="text-sm font-medium text-blue-800 mb-2">批次号信息</h4>
              <div className="grid grid-cols-2 gap-2 text-xs text-blue-700">
                <div>
                  <span className="font-medium">格式：</span>
                  <span className="font-mono">{batchInfo.suggestion?.format}</span>
                </div>
                <div>
                  <span className="font-medium">序号：</span>
                  <span>{batchInfo.sequence}</span>
                </div>
                <div>
                  <span className="font-medium">日期：</span>
                  <span>{batchInfo.dateStr}</span>
                </div>
                <div>
                  <span className="font-medium">类型：</span>
                  <span>{batchInfo.suggestion?.description}</span>
                </div>
              </div>
              {batchInfo.productInfo && (
                <div className="mt-2 pt-2 border-t border-blue-200">
                  <span className="text-xs font-medium text-blue-800">关联药品：</span>
                  <span className="text-xs text-blue-700 ml-1">
                    {batchInfo.productInfo.名称} ({batchInfo.productInfo.规格})
                  </span>
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
