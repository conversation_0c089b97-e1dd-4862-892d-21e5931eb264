import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

/**
 * 库存管理API - 适配新的库存表结构
 */

// 获取库存信息
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('product_id');
    const includeExpiring = searchParams.get('include_expiring') === 'true';
    const lowStock = searchParams.get('low_stock') === 'true';

    if (productId) {
      // 获取单个药品的库存信息
      const stockInfo = await query(`
        SELECT 
          s.编号 as id,
          s.药品编号 as product_id,
          p.名称 as product_name,
          p.规格 as specification,
          s.当前库存 as current_stock,
          s.最低库存 as min_stock,
          s.最高库存 as max_stock,
          s.平均成本价 as avg_cost_price,
          s.最后入库时间 as last_stock_in_time,
          s.最后出库时间 as last_stock_out_time,
          s.更新时间 as updated_at
        FROM 药品库存 s
        JOIN 药品信息 p ON s.药品编号 = p.编号
        WHERE s.药品编号 = ?
      `, [productId]);

      if (!stockInfo || stockInfo.length === 0) {
        return NextResponse.json({
          success: false,
          message: '未找到该药品的库存信息'
        }, { status: 404 });
      }

      // 获取批次信息
      const batches = await query(`
        SELECT 
          编号 as id,
          批次号 as batch_number,
          生产日期 as production_date,
          有效期 as expiry_date,
          入库数量 as inbound_quantity,
          当前数量 as current_quantity,
          供应商编号 as supplier_id,
          入库成本价 as inbound_cost_price,
          状态 as status,
          创建时间 as created_at,
          备注 as notes
        FROM 药品批次表
        WHERE 药品编号 = ? AND 状态 = 'active'
        ORDER BY 有效期 ASC
      `, [productId]);

      return NextResponse.json({
        success: true,
        data: {
          stock: stockInfo[0],
          batches: batches || []
        }
      });
    }

    // 获取所有药品的库存汇总
    let whereClause = '';
    let params = [];

    if (lowStock) {
      whereClause = 'WHERE s.当前库存 <= s.最低库存 AND s.最低库存 > 0';
    }

    const stockList = await query(`
      SELECT 
        s.编号 as id,
        s.药品编号 as product_id,
        p.名称 as product_name,
        p.规格 as specification,
        p.剂型 as dosage_form,
        c.名称 as category_name,
        s.当前库存 as current_stock,
        s.最低库存 as min_stock,
        s.最高库存 as max_stock,
        s.平均成本价 as avg_cost_price,
        s.最后入库时间 as last_stock_in_time,
        s.最后出库时间 as last_stock_out_time,
        CASE 
          WHEN s.当前库存 <= 0 THEN '缺货'
          WHEN s.当前库存 <= s.最低库存 AND s.最低库存 > 0 THEN '库存不足'
          ELSE '正常'
        END as stock_status
      FROM 药品库存 s
      JOIN 药品信息 p ON s.药品编号 = p.编号
      LEFT JOIN 药品分类 c ON p.分类编号 = c.编号
      ${whereClause}
      ORDER BY s.当前库存 ASC, p.名称 ASC
    `, params);

    // 如果需要包含即将过期的信息
    if (includeExpiring) {
      for (let stock of stockList) {
        const expiringBatches = await query(`
          SELECT COUNT(*) as count
          FROM 药品批次表
          WHERE 药品编号 = ? 
            AND 状态 = 'active' 
            AND 当前数量 > 0
            AND julianday(有效期) <= julianday('now', '+30 days')
        `, [stock.product_id]);
        
        stock.expiring_batches_count = expiringBatches[0]?.count || 0;
      }
    }

    return NextResponse.json({
      success: true,
      data: stockList
    });

  } catch (error) {
    console.error('获取库存信息失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取库存信息失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 更新库存设置（最低库存、最高库存等）
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { product_id, min_stock, max_stock } = body;

    if (!product_id) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数：product_id'
      }, { status: 400 });
    }

    // 检查药品库存记录是否存在
    const existingStock = await query(`
      SELECT 编号 FROM 药品库存 WHERE 药品编号 = ?
    `, [product_id]);

    if (!existingStock || existingStock.length === 0) {
      // 创建新的库存记录
      await run(`
        INSERT INTO 药品库存 (药品编号, 当前库存, 最低库存, 最高库存, 创建时间, 更新时间)
        VALUES (?, 0, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      `, [product_id, min_stock || 0, max_stock || 0]);
    } else {
      // 更新现有记录
      await run(`
        UPDATE 药品库存 
        SET 最低库存 = ?, 最高库存 = ?, 更新时间 = CURRENT_TIMESTAMP
        WHERE 药品编号 = ?
      `, [min_stock || 0, max_stock || 0, product_id]);
    }

    return NextResponse.json({
      success: true,
      message: '库存设置更新成功'
    });

  } catch (error) {
    console.error('更新库存设置失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '更新库存设置失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 库存统计信息
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    if (action === 'statistics') {
      // 获取库存统计信息
      const stats = await query(`
        SELECT 
          COUNT(*) as total_products,
          SUM(CASE WHEN 当前库存 > 0 THEN 1 ELSE 0 END) as in_stock_products,
          SUM(CASE WHEN 当前库存 <= 0 THEN 1 ELSE 0 END) as out_of_stock_products,
          SUM(CASE WHEN 当前库存 <= 最低库存 AND 最低库存 > 0 THEN 1 ELSE 0 END) as low_stock_products,
          SUM(当前库存) as total_stock_quantity,
          AVG(当前库存) as avg_stock_quantity
        FROM 药品库存
      `);

      // 获取即将过期的批次统计
      const expiringStats = await query(`
        SELECT 
          COUNT(*) as expiring_batches_count,
          SUM(当前数量) as expiring_quantity
        FROM 药品批次表
        WHERE 状态 = 'active' 
          AND 当前数量 > 0
          AND julianday(有效期) <= julianday('now', '+30 days')
      `);

      // 获取最近的库存变动
      const recentMovements = await query(`
        SELECT 
          r.编号 as id,
          r.药品编号 as product_id,
          p.名称 as product_name,
          r.操作类型 as operation_type,
          r.变动数量 as quantity_change,
          r.操作时间 as operation_time,
          r.操作人 as operator,
          r.备注 as notes
        FROM 库存变动记录 r
        JOIN 药品信息 p ON r.药品编号 = p.编号
        ORDER BY r.操作时间 DESC
        LIMIT 10
      `);

      return NextResponse.json({
        success: true,
        data: {
          overview: stats[0] || {},
          expiring: expiringStats[0] || {},
          recent_movements: recentMovements || []
        }
      });
    }

    return NextResponse.json({
      success: false,
      message: '不支持的操作类型'
    }, { status: 400 });

  } catch (error) {
    console.error('获取库存统计失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取库存统计失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
