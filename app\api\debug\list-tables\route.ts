import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

export async function GET() {
  try {
    // 获取所有表名
    const tables = await query(`
      SELECT name FROM sqlite_master 
      WHERE type='table' 
      ORDER BY name
    `);
    
    return NextResponse.json({
      success: true,
      data: tables
    });
  } catch (error) {
    console.error('获取表列表失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取表列表失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
