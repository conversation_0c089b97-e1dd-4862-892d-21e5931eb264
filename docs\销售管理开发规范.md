# 销售管理开发规范

## 概述

本文档定义了药店零售管理系统中销售管理模块的开发规范，包括药品追溯码扫描、库存联动出库、数据库设计等核心功能的实现标准。

## 功能架构

### 核心功能模块

1. **销售订单管理**
   - 订单创建与编辑
   - 客户信息管理
   - 订单状态跟踪

2. **药品追溯码管理**
   - 追溯码扫描识别
   - 码上放心平台集成
   - 追溯码使用记录

3. **库存联动出库**
   - 自动库存扣减
   - 批次管理（FIFO）
   - 库存记录生成

4. **支付结算**
   - 多种支付方式
   - 找零计算
   - 小票打印

## 数据库设计

### 核心数据表

#### 1. 销售订单表 (销售订单)
```sql
CREATE TABLE 销售订单 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  订单编号 TEXT UNIQUE NOT NULL,
  客户编号 INTEGER,
  总金额 DECIMAL(10,2) NOT NULL,
  折扣金额 DECIMAL(10,2) DEFAULT 0,
  应付金额 DECIMAL(10,2) NOT NULL,
  实付金额 DECIMAL(10,2) DEFAULT 0,
  找零金额 DECIMAL(10,2) DEFAULT 0,
  支付方式 TEXT DEFAULT 'cash',
  状态 TEXT DEFAULT 'pending',
  备注 TEXT,
  创建时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  更新时间 TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (客户编号) REFERENCES 客户信息(编号)
);
```

#### 2. 订单明细表 (订单明细)
```sql
CREATE TABLE 订单明细 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  订单编号 INTEGER NOT NULL,
  药品编号 INTEGER NOT NULL,
  数量 INTEGER NOT NULL,
  单价 DECIMAL(10,2) NOT NULL,
  小计 DECIMAL(10,2) NOT NULL,
  追溯码 TEXT,
  批次编号 INTEGER,
  FOREIGN KEY (订单编号) REFERENCES 销售订单(编号),
  FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
  FOREIGN KEY (批次编号) REFERENCES batch(编号)
);
```

#### 3. 药品追溯码记录表 (药品追溯码记录)
```sql
CREATE TABLE 药品追溯码记录 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  库存记录编号 INTEGER,
  销售订单编号 INTEGER,
  药品编号 INTEGER NOT NULL,
  追溯码 TEXT NOT NULL,
  操作类型 TEXT NOT NULL CHECK (操作类型 IN ('入库', '出库', '销售')),
  批次号 TEXT,
  有效期 DATE,
  生产厂家 TEXT,
  药品名称 TEXT,
  规格 TEXT,
  操作时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  码上放心上传状态 TEXT DEFAULT 'pending',
  码上放心响应 TEXT,
  备注 TEXT,
  FOREIGN KEY (库存记录编号) REFERENCES 库存记录(编号),
  FOREIGN KEY (销售订单编号) REFERENCES 销售订单(编号),
  FOREIGN KEY (药品编号) REFERENCES 药品信息(编号)
);
```

#### 4. 药品批次表 (药品批次表)
```sql
CREATE TABLE 药品批次表 (
  编号 INTEGER PRIMARY KEY AUTOINCREMENT,
  药品编号 INTEGER NOT NULL,
  批次号 TEXT NOT NULL,
  生产日期 DATE,
  有效期 DATE,
  数量 INTEGER NOT NULL DEFAULT 0,
  剩余数量 INTEGER NOT NULL DEFAULT 0,
  供应商编号 INTEGER,
  成本价 DECIMAL(10,2),
  状态 TEXT DEFAULT 'active' CHECK (状态 IN ('active', 'expired', 'depleted')),
  创建时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  更新时间 DATETIME DEFAULT CURRENT_TIMESTAMP,
  备注 TEXT,
  FOREIGN KEY (药品编号) REFERENCES 药品信息(编号),
  FOREIGN KEY (供应商编号) REFERENCES 供应商(编号)
);
```

## API接口规范

### 1. 追溯码扫描接口

**接口地址：** `POST /api/sales/scan-trace-code`

**请求参数：**
```typescript
interface ScanTraceCodeRequest {
  traceCode: string;    // 药品追溯码
  orderId?: number;     // 订单ID（可选）
}
```

**响应格式：**
```typescript
interface ScanTraceCodeResponse {
  success: boolean;
  message: string;
  data?: {
    traceCode: string;
    localProduct: {
      id: number;
      name: string;
      price: number;
      stockQuantity: number;
      // ... 其他药品信息
    };
    batchInfo: {
      batchNo: string;
      expireDate: string;
      manufacturer: string;
      // ... 其他批次信息
    };
    canSell: boolean;
    quantity: number;
  };
}
```

### 2. 销售出库处理接口

**接口地址：** `POST /api/sales/process-stock-out`

**请求参数：**
```typescript
interface ProcessStockOutRequest {
  orderId: number;
  items: Array<{
    productId: number;
    quantity: number;
    traceCode?: string;
    batchId?: number;
    batchInfo?: {
      batchNo: string;
      expireDate: string;
      manufacturer: string;
      // ... 其他批次信息
    };
  }>;
}
```

### 3. 数据库初始化接口

**接口地址：** `POST /api/sales/init-trace-schema`

用于初始化销售管理相关的数据库表结构。

## 业务流程规范

### 销售流程

1. **创建销售订单**
   - 生成订单编号（格式：SO-YYYYMMDDNNNN）
   - 初始化订单状态为 'pending'

2. **添加药品到订单**
   - 支持条形码扫描
   - 支持追溯码扫描
   - 自动计算小计和总计

3. **追溯码扫描流程**
   ```
   扫描追溯码 → 调用码上放心API → 获取药品信息 → 匹配本地药品 → 检查库存 → 添加到订单
   ```

4. **结算支付**
   - 计算应付金额（总金额 - 折扣）
   - 输入实付金额
   - 自动计算找零
   - 更新订单状态为 'completed'

5. **库存出库处理**
   - 扣减药品库存
   - 更新批次剩余数量（FIFO原则）
   - 生成库存记录
   - 记录追溯码使用信息

### 追溯码管理规范

1. **追溯码验证**
   - 检查追溯码格式
   - 验证是否已被使用
   - 调用码上放心平台验证

2. **追溯码记录**
   - 记录每次使用的追溯码
   - 关联销售订单和库存记录
   - 支持追溯查询

3. **批次管理**
   - 优先使用临近有效期的批次（FIFO）
   - 自动更新批次状态
   - 支持批次追溯

## 错误处理规范

### 常见错误场景

1. **追溯码相关错误**
   - 追溯码已被使用
   - 追溯码格式错误
   - 码上放心平台连接失败
   - 本地数据库中未找到对应药品

2. **库存相关错误**
   - 库存不足
   - 批次不存在或已过期
   - 库存记录创建失败

3. **订单相关错误**
   - 订单不存在
   - 订单状态不允许修改
   - 支付金额错误

### 错误处理原则

1. **事务一致性**
   - 使用数据库事务确保数据一致性
   - 出错时自动回滚

2. **用户友好提示**
   - 提供清晰的错误信息
   - 给出解决建议

3. **日志记录**
   - 记录详细的错误日志
   - 便于问题排查

## 性能优化规范

### 数据库优化

1. **索引设计**
   - 追溯码字段建立唯一索引
   - 订单编号、药品编号等建立索引
   - 时间字段建立索引用于查询优化

2. **查询优化**
   - 避免全表扫描
   - 使用合适的JOIN操作
   - 分页查询大数据集

### 接口优化

1. **缓存策略**
   - 药品信息缓存
   - 系统设置缓存

2. **异步处理**
   - 码上放心平台上传可异步处理
   - 大批量操作使用队列

## 安全规范

### 数据安全

1. **输入验证**
   - 验证所有用户输入
   - 防止SQL注入

2. **权限控制**
   - 操作员权限验证
   - 敏感操作记录日志

### 业务安全

1. **追溯码防重复使用**
   - 严格检查追溯码使用状态
   - 防止同一追溯码多次销售

2. **库存安全**
   - 防止负库存
   - 库存变动记录完整

## 测试规范

### 单元测试

1. **API接口测试**
   - 正常流程测试
   - 异常情况测试
   - 边界条件测试

2. **数据库操作测试**
   - 事务测试
   - 并发测试
   - 数据一致性测试

### 集成测试

1. **码上放心平台集成测试**
2. **完整销售流程测试**
3. **库存联动测试**

## 部署规范

### 数据库迁移

1. **表结构更新**
   - 使用迁移脚本
   - 保持向后兼容

2. **数据迁移**
   - 备份现有数据
   - 验证迁移结果

### 配置管理

1. **环境配置**
   - 开发、测试、生产环境分离
   - 敏感信息加密存储

2. **监控告警**
   - 接口性能监控
   - 错误率监控
   - 库存异常告警

## 更新记录

- **2025-07-08**: 初始版本创建
- 建立了销售管理模块的完整开发规范
- 定义了追溯码扫描和库存联动的业务流程
- 完善了数据库设计和API接口规范
- 添加了错误处理、性能优化、安全和测试规范
