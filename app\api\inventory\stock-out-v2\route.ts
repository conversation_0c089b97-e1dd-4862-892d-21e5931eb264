import { NextRequest, NextResponse } from 'next/server';
import { query, run } from '@/lib/db';

interface StockOutRequest {
  product_id: number;
  quantity: number;
  batch_id?: number; // 指定批次ID
  auto_select_batch?: boolean; // 自动选择批次（先进先出）
  reason: string; // 出库原因：'销售', '调整', '报损', '过期处理'
  related_order_type?: string; // 关联单据类型
  related_order_number?: string; // 关联单据编号
  unit_price?: number;
  stock_out_date: string;
  notes?: string;
  operator?: string;
  upload_to_mashangfangxin?: boolean;
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json() as StockOutRequest;
    const {
      product_id,
      quantity,
      batch_id,
      auto_select_batch,
      reason,
      related_order_type,
      related_order_number,
      unit_price,
      stock_out_date,
      notes,
      operator,
      upload_to_mashangfangxin
    } = body;

    // 验证数据
    if (!product_id || !quantity || !reason || !stock_out_date) {
      return NextResponse.json({
        success: false,
        message: '缺少必要参数：product_id, quantity, reason, stock_out_date'
      }, { status: 400 });
    }

    if (quantity <= 0) {
      return NextResponse.json({
        success: false,
        message: '出库数量必须大于0'
      }, { status: 400 });
    }

    if (!['销售', '调整', '报损', '过期处理', '盘点'].includes(reason)) {
      return NextResponse.json({
        success: false,
        message: '出库原因不正确'
      }, { status: 400 });
    }

    if (isNaN(Date.parse(stock_out_date))) {
      return NextResponse.json({
        success: false,
        message: '出库日期格式不正确'
      }, { status: 400 });
    }

    // 开始事务
    await run('BEGIN TRANSACTION');

    try {
      // 1. 检查产品是否存在
      const product = await query('SELECT * FROM 药品信息 WHERE 编号 = ?', [product_id]);
      if (!product || product.length === 0) {
        throw new Error('产品不存在');
      }

      // 2. 获取当前库存
      const stockRecord = await query('SELECT * FROM 药品库存 WHERE 药品编号 = ?', [product_id]);
      if (!stockRecord || stockRecord.length === 0) {
        throw new Error('该药品没有库存记录');
      }

      const currentStock = stockRecord[0].当前库存 || 0;
      if (currentStock < quantity) {
        throw new Error(`库存不足，当前库存：${currentStock}，需要出库：${quantity}`);
      }

      // 3. 选择批次
      let selectedBatches = [];
      
      if (batch_id) {
        // 指定批次出库
        const batch = await query(`
          SELECT * FROM 药品批次表 
          WHERE 编号 = ? AND 药品编号 = ? AND 状态 = 'active' AND 当前数量 > 0
        `, [batch_id, product_id]);
        
        if (!batch || batch.length === 0) {
          throw new Error('指定的批次不存在或无库存');
        }
        
        if (batch[0].当前数量 < quantity) {
          throw new Error(`指定批次库存不足，当前数量：${batch[0].当前数量}，需要出库：${quantity}`);
        }
        
        selectedBatches.push({
          batch: batch[0],
          quantity: quantity
        });
      } else if (auto_select_batch) {
        // 自动选择批次（先进先出）
        const availableBatches = await query(`
          SELECT * FROM 药品批次表 
          WHERE 药品编号 = ? AND 状态 = 'active' AND 当前数量 > 0
          ORDER BY 有效期 ASC, 创建时间 ASC
        `, [product_id]);
        
        if (!availableBatches || availableBatches.length === 0) {
          throw new Error('没有可用的批次');
        }
        
        let remainingQuantity = quantity;
        for (const batch of availableBatches) {
          if (remainingQuantity <= 0) break;
          
          const batchQuantity = Math.min(batch.当前数量, remainingQuantity);
          selectedBatches.push({
            batch: batch,
            quantity: batchQuantity
          });
          
          remainingQuantity -= batchQuantity;
        }
        
        if (remainingQuantity > 0) {
          throw new Error('可用批次库存总量不足');
        }
      } else {
        throw new Error('必须指定批次ID或启用自动选择批次');
      }

      // 4. 执行出库操作
      let totalOutQuantity = 0;
      const batchOperations = [];
      
      for (const { batch, quantity: batchQuantity } of selectedBatches) {
        const newBatchQuantity = batch.当前数量 - batchQuantity;
        
        // 更新批次数量
        await run(`
          UPDATE 药品批次表 
          SET 当前数量 = ?, 更新时间 = CURRENT_TIMESTAMP
          WHERE 编号 = ?
        `, [newBatchQuantity, batch.编号]);
        
        // 如果批次用完，更新状态
        if (newBatchQuantity <= 0) {
          await run(`
            UPDATE 药品批次表 
            SET 状态 = 'depleted'
            WHERE 编号 = ?
          `, [batch.编号]);
        }
        
        // 记录批次操作
        batchOperations.push({
          batch_id: batch.编号,
          batch_number: batch.批次号,
          quantity: batchQuantity,
          before_quantity: batch.当前数量,
          after_quantity: newBatchQuantity
        });
        
        totalOutQuantity += batchQuantity;
      }

      // 5. 更新药品库存汇总
      const newStock = currentStock - totalOutQuantity;
      await run(`
        UPDATE 药品库存 
        SET 当前库存 = ?, 最后出库时间 = CURRENT_TIMESTAMP, 更新时间 = CURRENT_TIMESTAMP
        WHERE 药品编号 = ?
      `, [newStock, product_id]);

      // 6. 记录库存变动
      for (const operation of batchOperations) {
        await run(`
          INSERT INTO 库存变动记录 (
            药品编号, 批次编号, 操作类型, 变动数量, 变动前数量, 变动后数量,
            单价, 总金额, 关联单据类型, 关联单据编号,
            操作人, 操作时间, 备注
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          product_id,
          operation.batch_id,
          '出库',
          -operation.quantity, // 负数表示出库
          operation.before_quantity,
          operation.after_quantity,
          unit_price || null,
          unit_price ? (unit_price * operation.quantity) : null,
          related_order_type || null,
          related_order_number || null,
          operator || '系统操作员',
          stock_out_date,
          notes || `${reason}出库`
        ]);
      }

      // 7. 上传到码上放心平台（如果需要）
      if (upload_to_mashangfangxin && reason === '销售') {
        // 这里可以添加码上放心平台上传逻辑
        console.log('上传销售出库到码上放心平台');
      }

      // 提交事务
      await run('COMMIT');

      // 获取更新后的库存信息
      const updatedStock = await query(`
        SELECT 
          s.当前库存 as current_stock,
          s.最低库存 as min_stock,
          s.最高库存 as max_stock
        FROM 药品库存 s
        WHERE s.药品编号 = ?
      `, [product_id]);

      return NextResponse.json({
        success: true,
        message: '出库成功',
        data: {
          quantity_out: totalOutQuantity,
          new_stock: newStock,
          batch_operations: batchOperations,
          stock_info: updatedStock[0] || {}
        }
      });

    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('出库操作失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '出库操作失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}

// 获取出库历史记录
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('product_id');
    const reason = searchParams.get('reason');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    let whereClause = "WHERE r.操作类型 = '出库'";
    let params = [];

    if (productId) {
      whereClause += " AND r.药品编号 = ?";
      params.push(productId);
    }

    if (reason) {
      whereClause += " AND r.备注 LIKE ?";
      params.push(`%${reason}%`);
    }

    const records = await query(`
      SELECT 
        r.编号 as id,
        r.药品编号 as product_id,
        p.名称 as product_name,
        r.批次编号 as batch_id,
        b.批次号 as batch_number,
        ABS(r.变动数量) as quantity,
        r.变动前数量 as before_quantity,
        r.变动后数量 as after_quantity,
        r.单价 as unit_price,
        r.总金额 as total_amount,
        r.关联单据类型 as related_order_type,
        r.关联单据编号 as related_order_number,
        r.操作人 as operator,
        r.操作时间 as operation_time,
        r.备注 as notes
      FROM 库存变动记录 r
      JOIN 药品信息 p ON r.药品编号 = p.编号
      LEFT JOIN 药品批次表 b ON r.批次编号 = b.编号
      ${whereClause}
      ORDER BY r.操作时间 DESC
      LIMIT ? OFFSET ?
    `, [...params, limit, offset]);

    return NextResponse.json({
      success: true,
      data: records || []
    });

  } catch (error) {
    console.error('获取出库记录失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '获取出库记录失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
