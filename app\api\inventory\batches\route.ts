import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const productId = searchParams.get('product_id');
    
    if (!productId) {
      return NextResponse.json({ 
        success: false, 
        message: '缺少必要参数' 
      }, { status: 400 });
    }

    // 使用批次视图进行查询，如果视图不存在则直接查询batch表
    let batches;
    try {
      batches = await query(`
        SELECT * FROM batch_view
        WHERE product_id = ?
        ORDER BY expiry_date ASC
      `, [productId]);
    } catch (viewError) {
      console.log('批次视图不存在，尝试直接查询药品批次表...');
      try {
        batches = await query(`
          SELECT
            b.编号 as id,
            b.药品编号 as product_id,
            b.批次号 as batch_number,
            b.生产日期 as production_date,
            b.有效期 as expiry_date,
            b.数量 as quantity,
            b.剩余数量 as remaining_quantity,
            b.供应商编号 as supplier_id,
            s.名称 as supplier_name,
            b.成本价 as purchase_price,
            b.状态 as status,
            b.创建时间 as created_at,
            b.更新时间 as updated_at,
            b.备注 as notes
          FROM 药品批次表 b
          LEFT JOIN 供应商 s ON b.供应商编号 = s.编号
          WHERE b.药品编号 = ?
          ORDER BY b.有效期 ASC
        `, [productId]);
      } catch (batchError) {
        console.log('药品批次表也不存在，尝试从库存记录表查询批次信息...');
        batches = await query(`
          SELECT DISTINCT
            ROW_NUMBER() OVER (ORDER BY 批次号) as id,
            药品编号 as product_id,
            批次号 as batch_number,
            有效期 as expiry_date,
            SUM(数量变化) as quantity,
            SUM(数量变化) as remaining_quantity,
            供应商编号 as supplier_id,
            s.名称 as supplier_name,
            成本价 as purchase_price,
            'active' as status,
            MIN(操作时间) as created_at,
            MAX(操作时间) as updated_at,
            GROUP_CONCAT(备注, '; ') as notes
          FROM 库存记录 lr
          LEFT JOIN 供应商 s ON lr.供应商编号 = s.编号
          WHERE lr.药品编号 = ? AND lr.批次号 IS NOT NULL AND lr.批次号 != ''
          GROUP BY lr.药品编号, lr.批次号, lr.有效期, lr.供应商编号
          HAVING SUM(数量变化) > 0
          ORDER BY lr.有效期 ASC
        `, [productId]);
      }
    }

    return NextResponse.json({ 
      success: true, 
      data: batches
    });
  } catch (error) {
    console.error('获取批次数据错误:', error);
    return NextResponse.json({ 
      success: false, 
      message: '服务器处理批次数据时出错' 
    }, { status: 500 });
  }
} 