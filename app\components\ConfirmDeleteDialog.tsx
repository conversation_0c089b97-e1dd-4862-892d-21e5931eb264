'use client';

import React from 'react';

interface ConfirmDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message: string;
  itemName?: string;
  confirmText?: string;
  cancelText?: string;
}

/**
 * 可复用的删除确认对话框组件
 * 用于替换浏览器原生的confirm弹窗
 * 参考库存管理页面的样式设计，使用蓝色字体主题
 */
export default function ConfirmDeleteDialog({
  isOpen,
  onClose,
  onConfirm,
  title = '确认删除',
  message,
  itemName,
  confirmText = '确认删除',
  cancelText = '取消'
}: ConfirmDeleteDialogProps) {
  if (!isOpen) return null;

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4"
      style={{
        zIndex: 10000,
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
      }}
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div className="bg-white rounded-xl shadow-lg max-w-md w-full mx-4">
        <div className="p-6">
          {/* 标题 */}
          <div className="flex items-center mb-4">
            <div className="flex-shrink-0">
              <svg
                className="h-6 w-6 text-red-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h3 className="ml-3 text-lg font-medium leading-6 text-blue-700">
              {title}
            </h3>
          </div>

          {/* 消息内容 */}
          <div className="mb-6">
            <div className="p-4 bg-yellow-50 border border-yellow-300 rounded-md">
              <p className="text-blue-700 text-sm">
                {message}
                {itemName && (
                  <span className="font-medium"> "{itemName}"</span>
                )}
              </p>
              <p className="text-gray-600 text-xs mt-2">
                此操作不可撤销，请谨慎操作。
              </p>
            </div>
          </div>

          {/* 按钮组 */}
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
            >
              {cancelText}
            </button>
            <button
              type="button"
              onClick={handleConfirm}
              className="px-4 py-2 text-sm font-medium text-white bg-red-500 border border-transparent rounded-md hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors"
            >
              {confirmText}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
